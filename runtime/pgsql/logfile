2025-05-28 12:10:09.634 CST [29460] LOG:  starting PostgreSQL 16.4, compiled by Visual C++ build 1940, 64-bit
2025-05-28 12:10:09.637 CST [29460] LOG:  listening on IPv6 address "::1", port 5432
2025-05-28 12:10:09.637 CST [29460] LOG:  listening on IPv4 address "127.0.0.1", port 5432
2025-05-28 12:10:09.730 CST [4808] LOG:  database system shutdown was interrupted; last known up at 2025-05-28 12:09:52 CST
2025-05-28 12:10:10.110 CST [4808] LOG:  database system was not properly shut down; automatic recovery in progress
2025-05-28 12:10:10.112 CST [4808] LOG:  could not open directory "pg_snapshots": No such file or directory
2025-05-28 12:10:10.112 CST [4808] LOG:  redo starts at 0/5EA8600
2025-05-28 12:10:10.112 CST [4808] LOG:  invalid record length at 0/5EA8638: expected at least 24, got 0
2025-05-28 12:10:10.113 CST [4808] LOG:  redo done at 0/5EA8600 system usage: CPU: user: 0.00 s, system: 0.00 s, elapsed: 0.00 s
2025-05-28 12:10:10.117 CST [16112] LOG:  checkpoint starting: end-of-recovery immediate wait
2025-05-28 12:10:10.117 CST [16112] ERROR:  could not open directory "pg_logical/snapshots": No such file or directory
2025-05-28 12:10:10.117 CST [4808] FATAL:  checkpoint request failed
2025-05-28 12:10:10.117 CST [4808] HINT:  Consult recent messages in the server log for details.
2025-05-28 12:10:10.121 CST [29460] LOG:  startup process (PID 4808) exited with exit code 1
2025-05-28 12:10:10.121 CST [29460] LOG:  terminating any other active server processes
2025-05-28 12:10:10.124 CST [29460] LOG:  shutting down due to startup process failure
2025-05-28 12:10:10.125 CST [29460] LOG:  database system is shut down
2025-05-28 12:10:41.563 CST [17636] LOG:  starting PostgreSQL 16.4, compiled by Visual C++ build 1940, 64-bit
2025-05-28 12:10:41.567 CST [17636] LOG:  listening on IPv6 address "::1", port 5432
2025-05-28 12:10:41.567 CST [17636] LOG:  listening on IPv4 address "127.0.0.1", port 5432
2025-05-28 12:10:41.656 CST [10164] LOG:  database system shutdown was interrupted; last known up at 2025-05-28 12:10:10 CST
2025-05-28 12:10:42.000 CST [10164] LOG:  database system was not properly shut down; automatic recovery in progress
2025-05-28 12:10:42.002 CST [10164] LOG:  could not open directory "pg_snapshots": No such file or directory
2025-05-28 12:10:42.002 CST [10164] LOG:  redo starts at 0/5EA8600
2025-05-28 12:10:42.002 CST [10164] LOG:  invalid record length at 0/5EA8638: expected at least 24, got 0
2025-05-28 12:10:42.002 CST [10164] LOG:  redo done at 0/5EA8600 system usage: CPU: user: 0.00 s, system: 0.00 s, elapsed: 0.00 s
2025-05-28 12:10:42.006 CST [26456] LOG:  checkpoint starting: end-of-recovery immediate wait
2025-05-28 12:10:42.009 CST [26456] LOG:  checkpoint complete: wrote 2 buffers (0.0%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.001 s, sync=0.001 s, total=0.005 s; sync files=3, longest=0.001 s, average=0.001 s; distance=0 kB, estimate=0 kB; lsn=0/5EA8638, redo lsn=0/5EA8638
2025-05-28 12:10:42.021 CST [17636] LOG:  database system is ready to accept connections
2025-05-28 12:25:01.116 CST [17636] LOG:  received fast shutdown request
2025-05-28 12:25:01.119 CST [17636] LOG:  aborting any active transactions
2025-05-28 12:25:01.130 CST [17636] LOG:  background worker "logical replication launcher" (PID 11936) exited with exit code 1
2025-05-28 12:25:01.130 CST [26456] LOG:  shutting down
2025-05-28 12:25:01.131 CST [26456] LOG:  checkpoint starting: shutdown immediate
2025-05-28 12:25:01.137 CST [26456] LOG:  checkpoint complete: wrote 0 buffers (0.0%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.001 s, sync=0.001 s, total=0.008 s; sync files=0, longest=0.000 s, average=0.000 s; distance=0 kB, estimate=0 kB; lsn=0/5EA86E8, redo lsn=0/5EA86E8
2025-05-28 12:25:01.143 CST [17636] LOG:  database system is shut down
2025-05-28 12:25:05.730 CST [9356] LOG:  starting PostgreSQL 16.4, compiled by Visual C++ build 1940, 64-bit
2025-05-28 12:25:05.735 CST [9356] LOG:  listening on IPv6 address "::1", port 5432
2025-05-28 12:25:05.735 CST [9356] LOG:  listening on IPv4 address "127.0.0.1", port 5432
2025-05-28 12:25:05.821 CST [11196] LOG:  database system was shut down at 2025-05-28 12:25:01 CST
2025-05-28 12:25:05.843 CST [9356] LOG:  database system is ready to accept connections
2025-05-28 12:25:21.742 CST [9356] LOG:  received fast shutdown request
2025-05-28 12:25:21.745 CST [9356] LOG:  aborting any active transactions
2025-05-28 12:25:21.751 CST [9356] LOG:  background worker "logical replication launcher" (PID 26732) exited with exit code 1
2025-05-28 12:25:21.752 CST [19268] LOG:  shutting down
2025-05-28 12:25:21.752 CST [19268] LOG:  checkpoint starting: shutdown immediate
2025-05-28 12:25:21.757 CST [19268] LOG:  checkpoint complete: wrote 2 buffers (0.0%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.002 s, sync=0.001 s, total=0.006 s; sync files=3, longest=0.001 s, average=0.001 s; distance=0 kB, estimate=0 kB; lsn=0/5EA8798, redo lsn=0/5EA8798
2025-05-28 12:25:21.762 CST [9356] LOG:  database system is shut down
2025-05-28 12:25:38.432 CST [31348] LOG:  starting PostgreSQL 16.4, compiled by Visual C++ build 1940, 64-bit
2025-05-28 12:25:38.436 CST [31348] LOG:  listening on IPv6 address "::1", port 5432
2025-05-28 12:25:38.436 CST [31348] LOG:  listening on IPv4 address "127.0.0.1", port 5432
2025-05-28 12:25:38.506 CST [20060] LOG:  database system was shut down at 2025-05-28 12:25:21 CST
2025-05-28 12:25:38.523 CST [31348] LOG:  database system is ready to accept connections
2025-05-28 12:26:25.076 CST [31348] LOG:  received fast shutdown request
2025-05-28 12:26:25.079 CST [31348] LOG:  aborting any active transactions
2025-05-28 12:26:25.080 CST [16660] FATAL:  terminating connection due to administrator command
2025-05-28 12:26:25.080 CST [9668] FATAL:  terminating connection due to administrator command
2025-05-28 12:26:25.093 CST [31348] LOG:  background worker "logical replication launcher" (PID 2296) exited with exit code 1
2025-05-28 12:26:25.096 CST [29520] LOG:  shutting down
2025-05-28 12:26:25.097 CST [29520] LOG:  checkpoint starting: shutdown immediate
2025-05-28 12:26:25.103 CST [29520] LOG:  checkpoint complete: wrote 2 buffers (0.0%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.002 s, sync=0.001 s, total=0.008 s; sync files=3, longest=0.001 s, average=0.001 s; distance=0 kB, estimate=0 kB; lsn=0/5EA8848, redo lsn=0/5EA8848
2025-05-28 12:26:25.109 CST [31348] LOG:  database system is shut down
2025-05-28 12:28:43.388 CST [32568] LOG:  starting PostgreSQL 16.4, compiled by Visual C++ build 1940, 64-bit
2025-05-28 12:28:43.392 CST [32568] LOG:  listening on IPv6 address "::1", port 5432
2025-05-28 12:28:43.392 CST [32568] LOG:  listening on IPv4 address "127.0.0.1", port 5432
2025-05-28 12:28:43.487 CST [22856] LOG:  database system was shut down at 2025-05-28 12:26:25 CST
2025-05-28 12:28:43.505 CST [32568] LOG:  database system is ready to accept connections
2025-05-28 12:31:36.468 CST [32568] LOG:  received fast shutdown request
2025-05-28 12:31:36.472 CST [32568] LOG:  aborting any active transactions
2025-05-28 12:31:36.473 CST [16896] FATAL:  terminating connection due to administrator command
2025-05-28 12:31:36.474 CST [4204] FATAL:  terminating connection due to administrator command
2025-05-28 12:31:36.481 CST [32568] LOG:  background worker "logical replication launcher" (PID 33524) exited with exit code 1
2025-05-28 12:31:36.481 CST [13040] LOG:  shutting down
2025-05-28 12:31:36.482 CST [13040] LOG:  checkpoint starting: shutdown immediate
2025-05-28 12:31:36.491 CST [13040] LOG:  checkpoint complete: wrote 2 buffers (0.0%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.002 s, sync=0.002 s, total=0.010 s; sync files=3, longest=0.001 s, average=0.001 s; distance=0 kB, estimate=0 kB; lsn=0/5EA88F8, redo lsn=0/5EA88F8
2025-05-28 12:31:36.497 CST [32568] LOG:  database system is shut down
2025-05-28 12:32:43.857 CST [33296] LOG:  starting PostgreSQL 16.4, compiled by Visual C++ build 1940, 64-bit
2025-05-28 12:32:43.862 CST [33296] LOG:  listening on IPv6 address "::1", port 5432
2025-05-28 12:32:43.862 CST [33296] LOG:  listening on IPv4 address "127.0.0.1", port 5432
2025-05-28 12:32:43.966 CST [28952] LOG:  database system was shut down at 2025-05-28 12:31:36 CST
2025-05-28 12:32:43.987 CST [33296] LOG:  database system is ready to accept connections
2025-05-28 12:34:00.416 CST [33296] LOG:  received fast shutdown request
2025-05-28 12:34:00.418 CST [33296] LOG:  aborting any active transactions
2025-05-28 12:34:00.418 CST [31816] FATAL:  terminating connection due to administrator command
2025-05-28 12:34:00.418 CST [22404] FATAL:  terminating connection due to administrator command
2025-05-28 12:34:00.419 CST [17204] FATAL:  terminating connection due to administrator command
2025-05-28 12:34:00.419 CST [16088] FATAL:  terminating connection due to administrator command
2025-05-28 12:34:00.419 CST [21076] FATAL:  terminating connection due to administrator command
2025-05-28 12:34:00.419 CST [28076] FATAL:  terminating connection due to administrator command
2025-05-28 12:34:00.419 CST [2748] FATAL:  terminating connection due to administrator command
2025-05-28 12:34:00.425 CST [33296] LOG:  background worker "logical replication launcher" (PID 16572) exited with exit code 1
2025-05-28 12:34:00.445 CST [17928] LOG:  shutting down
2025-05-28 12:34:00.446 CST [17928] LOG:  checkpoint starting: shutdown immediate
2025-05-28 12:34:00.487 CST [10956] FATAL:  the database system is shutting down
2025-05-28 12:34:00.492 CST [17928] LOG:  checkpoint complete: wrote 115 buffers (0.7%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.012 s, sync=0.029 s, total=0.048 s; sync files=68, longest=0.002 s, average=0.001 s; distance=440 kB, estimate=440 kB; lsn=0/5F169F8, redo lsn=0/5F169F8
2025-05-28 12:34:00.505 CST [33296] LOG:  database system is shut down
2025-05-28 12:35:02.688 CST [18088] LOG:  starting PostgreSQL 16.4, compiled by Visual C++ build 1940, 64-bit
2025-05-28 12:35:02.692 CST [18088] LOG:  listening on IPv6 address "::1", port 5432
2025-05-28 12:35:02.692 CST [18088] LOG:  listening on IPv4 address "127.0.0.1", port 5432
2025-05-28 12:35:02.782 CST [29236] LOG:  database system was shut down at 2025-05-28 12:34:00 CST
2025-05-28 12:35:02.806 CST [18088] LOG:  database system is ready to accept connections
2025-05-28 12:36:00.427 CST [18088] LOG:  received fast shutdown request
2025-05-28 12:36:00.430 CST [18088] LOG:  aborting any active transactions
2025-05-28 12:36:00.431 CST [15816] FATAL:  terminating connection due to administrator command
2025-05-28 12:36:00.431 CST [34236] FATAL:  terminating connection due to administrator command
2025-05-28 12:36:00.431 CST [17492] FATAL:  terminating connection due to administrator command
2025-05-28 12:36:00.431 CST [29292] FATAL:  terminating connection due to administrator command
2025-05-28 12:36:00.431 CST [20388] FATAL:  terminating connection due to administrator command
2025-05-28 12:36:00.431 CST [484] FATAL:  terminating connection due to administrator command
2025-05-28 12:36:00.431 CST [20920] FATAL:  terminating connection due to administrator command
2025-05-28 12:36:00.432 CST [30792] FATAL:  terminating connection due to administrator command
2025-05-28 12:36:00.448 CST [18088] LOG:  background worker "logical replication launcher" (PID 3116) exited with exit code 1
2025-05-28 12:36:00.452 CST [18268] LOG:  shutting down
2025-05-28 12:36:00.453 CST [18268] LOG:  checkpoint starting: shutdown immediate
2025-05-28 12:36:00.494 CST [18268] LOG:  checkpoint complete: wrote 158 buffers (1.0%); 0 WAL file(s) added, 0 removed, 1 recycled; write=0.013 s, sync=0.020 s, total=0.043 s; sync files=40, longest=0.002 s, average=0.001 s; distance=988 kB, estimate=988 kB; lsn=0/600DC80, redo lsn=0/600DC80
2025-05-28 12:36:00.495 CST [6856] FATAL:  the database system is shutting down
2025-05-28 12:36:00.507 CST [18088] LOG:  database system is shut down
2025-05-28 12:37:10.048 CST [11440] LOG:  starting PostgreSQL 16.4, compiled by Visual C++ build 1940, 64-bit
2025-05-28 12:37:10.056 CST [11440] LOG:  listening on IPv6 address "::1", port 5432
2025-05-28 12:37:10.056 CST [11440] LOG:  listening on IPv4 address "127.0.0.1", port 5432
2025-05-28 12:37:10.281 CST [34260] LOG:  database system was shut down at 2025-05-28 12:36:00 CST
2025-05-28 12:37:10.336 CST [11440] LOG:  database system is ready to accept connections
2025-05-28 12:42:10.176 CST [31888] LOG:  checkpoint starting: time
2025-05-28 12:42:11.826 CST [31888] LOG:  checkpoint complete: wrote 17 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=1.643 s, sync=0.005 s, total=1.651 s; sync files=15, longest=0.003 s, average=0.001 s; distance=18 kB, estimate=18 kB; lsn=0/60127C0, redo lsn=0/6012788
2025-05-28 13:04:09.737 CST [31888] LOG:  checkpoint starting: time
2025-05-28 13:04:11.671 CST [31888] LOG:  checkpoint complete: wrote 20 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=1.859 s, sync=0.042 s, total=1.935 s; sync files=17, longest=0.027 s, average=0.003 s; distance=42 kB, estimate=42 kB; lsn=0/601D360, redo lsn=0/601D328
2025-05-28 13:09:09.673 CST [31888] LOG:  checkpoint starting: time
2025-05-28 13:09:10.339 CST [31888] LOG:  checkpoint complete: wrote 7 buffers (0.0%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.657 s, sync=0.004 s, total=0.667 s; sync files=5, longest=0.002 s, average=0.001 s; distance=30 kB, estimate=41 kB; lsn=0/6024DB8, redo lsn=0/6024D80
2025-05-28 14:26:08.063 CST [31888] LOG:  checkpoint starting: time
2025-05-28 14:26:10.255 CST [31888] LOG:  checkpoint complete: wrote 23 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=2.172 s, sync=0.014 s, total=2.192 s; sync files=20, longest=0.002 s, average=0.001 s; distance=45 kB, estimate=45 kB; lsn=0/6030368, redo lsn=0/6030330
2025-05-28 14:29:47.609 CST [11440] LOG:  received fast shutdown request
2025-05-28 14:29:47.612 CST [11440] LOG:  aborting any active transactions
2025-05-28 14:29:47.613 CST [13932] FATAL:  terminating connection due to administrator command
2025-05-28 14:29:47.613 CST [34004] FATAL:  terminating connection due to administrator command
2025-05-28 14:29:47.614 CST [6920] FATAL:  terminating connection due to administrator command
2025-05-28 14:29:47.615 CST [26712] FATAL:  terminating connection due to administrator command
2025-05-28 14:29:47.615 CST [16992] FATAL:  terminating connection due to administrator command
2025-05-28 14:29:47.615 CST [34064] FATAL:  terminating connection due to administrator command
2025-05-28 14:29:47.615 CST [12116] FATAL:  terminating connection due to administrator command
2025-05-28 14:29:47.615 CST [15164] FATAL:  terminating connection due to administrator command
2025-05-28 14:29:47.615 CST [19492] FATAL:  terminating connection due to administrator command
2025-05-28 14:29:47.615 CST [29932] FATAL:  terminating connection due to administrator command
2025-05-28 14:29:47.624 CST [11440] LOG:  background worker "logical replication launcher" (PID 2156) exited with exit code 1
2025-05-28 14:29:47.637 CST [31888] LOG:  shutting down
2025-05-28 14:29:47.638 CST [31888] LOG:  checkpoint starting: shutdown immediate
2025-05-28 14:29:47.646 CST [31888] LOG:  checkpoint complete: wrote 0 buffers (0.0%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.001 s, sync=0.001 s, total=0.010 s; sync files=0, longest=0.000 s, average=0.000 s; distance=0 kB, estimate=40 kB; lsn=0/6030418, redo lsn=0/6030418
2025-05-28 14:29:47.669 CST [4328] FATAL:  the database system is shutting down
2025-05-28 14:29:47.679 CST [11440] LOG:  database system is shut down
2025-05-28 14:31:33.100 CST [34640] LOG:  starting PostgreSQL 16.4, compiled by Visual C++ build 1940, 64-bit
2025-05-28 14:31:33.104 CST [34640] LOG:  listening on IPv6 address "::1", port 5432
2025-05-28 14:31:33.104 CST [34640] LOG:  listening on IPv4 address "127.0.0.1", port 5432
2025-05-28 14:31:33.171 CST [29796] LOG:  database system was shut down at 2025-05-28 14:29:47 CST
2025-05-28 14:31:33.187 CST [34640] LOG:  database system is ready to accept connections
2025-05-28 14:36:33.175 CST [15484] LOG:  checkpoint starting: time
2025-05-28 14:36:37.436 CST [15484] LOG:  checkpoint complete: wrote 43 buffers (0.3%); 0 WAL file(s) added, 0 removed, 0 recycled; write=4.247 s, sync=0.011 s, total=4.261 s; sync files=37, longest=0.002 s, average=0.001 s; distance=55 kB, estimate=55 kB; lsn=0/603E300, redo lsn=0/603E2C8
2025-05-28 14:46:33.466 CST [15484] LOG:  checkpoint starting: time
2025-05-28 14:46:34.246 CST [15484] LOG:  checkpoint complete: wrote 10 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.767 s, sync=0.006 s, total=0.781 s; sync files=10, longest=0.003 s, average=0.001 s; distance=21 kB, estimate=52 kB; lsn=0/6043A58, redo lsn=0/6043A20
2025-05-28 14:58:27.624 CST [15484] LOG:  checkpoint starting: time
2025-05-28 14:58:29.166 CST [15484] LOG:  checkpoint complete: wrote 17 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=1.532 s, sync=0.006 s, total=1.543 s; sync files=14, longest=0.003 s, average=0.001 s; distance=43 kB, estimate=51 kB; lsn=0/604E668, redo lsn=0/604E630
2025-05-28 15:53:43.479 CST [15484] LOG:  checkpoint starting: time
2025-05-28 15:53:46.132 CST [15484] LOG:  checkpoint complete: wrote 27 buffers (0.2%); 0 WAL file(s) added, 0 removed, 0 recycled; write=2.640 s, sync=0.008 s, total=2.653 s; sync files=22, longest=0.003 s, average=0.001 s; distance=77 kB, estimate=77 kB; lsn=0/6061C48, redo lsn=0/6061C10
2025-05-28 16:00:51.554 CST [15484] LOG:  checkpoint starting: time
2025-05-28 16:00:52.873 CST [15484] LOG:  checkpoint complete: wrote 13 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=1.308 s, sync=0.006 s, total=1.320 s; sync files=11, longest=0.003 s, average=0.001 s; distance=33 kB, estimate=73 kB; lsn=0/606A3D0, redo lsn=0/606A398
2025-05-28 16:11:31.317 CST [15484] LOG:  checkpoint starting: time
2025-05-28 16:11:32.745 CST [15484] LOG:  checkpoint complete: wrote 16 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=1.418 s, sync=0.005 s, total=1.429 s; sync files=15, longest=0.002 s, average=0.001 s; distance=43 kB, estimate=70 kB; lsn=0/60752F0, redo lsn=0/60752B8
2025-05-28 16:16:31.751 CST [15484] LOG:  checkpoint starting: time
2025-05-28 16:16:32.516 CST [15484] LOG:  checkpoint complete: wrote 10 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.758 s, sync=0.004 s, total=0.766 s; sync files=10, longest=0.002 s, average=0.001 s; distance=25 kB, estimate=65 kB; lsn=0/607B738, redo lsn=0/607B700
2025-05-28 16:26:31.544 CST [15484] LOG:  checkpoint starting: time
2025-05-28 16:29:49.785 CST [15484] LOG:  checkpoint complete: wrote 1825 buffers (11.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=198.130 s, sync=0.107 s, total=198.242 s; sync files=577, longest=0.002 s, average=0.001 s; distance=12230 kB, estimate=12230 kB; lsn=0/6CB3598, redo lsn=0/6C6D2B8
2025-05-28 16:36:31.809 CST [15484] LOG:  checkpoint starting: time
2025-05-28 16:36:36.820 CST [15484] LOG:  checkpoint complete: wrote 49 buffers (0.3%); 0 WAL file(s) added, 0 removed, 0 recycled; write=4.998 s, sync=0.009 s, total=5.012 s; sync files=35, longest=0.002 s, average=0.001 s; distance=283 kB, estimate=11036 kB; lsn=0/6CB40C8, redo lsn=0/6CB4090
2025-05-28 16:46:31.823 CST [15484] LOG:  checkpoint starting: time
2025-05-28 16:46:32.602 CST [15484] LOG:  checkpoint complete: wrote 10 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.771 s, sync=0.004 s, total=0.780 s; sync files=10, longest=0.002 s, average=0.001 s; distance=22 kB, estimate=9934 kB; lsn=0/6CB9A08, redo lsn=0/6CB99D0
2025-05-28 21:46:49.630 CST [15484] LOG:  checkpoint starting: time
2025-05-28 21:46:52.808 CST [15484] LOG:  checkpoint complete: wrote 32 buffers (0.2%); 0 WAL file(s) added, 0 removed, 0 recycled; write=3.151 s, sync=0.019 s, total=3.178 s; sync files=24, longest=0.002 s, average=0.001 s; distance=52 kB, estimate=8946 kB; lsn=0/6CC6E28, redo lsn=0/6CC6DB8
2025-05-28 21:51:49.815 CST [15484] LOG:  checkpoint starting: time
2025-05-28 21:51:54.262 CST [15484] LOG:  checkpoint complete: wrote 43 buffers (0.3%); 0 WAL file(s) added, 0 removed, 0 recycled; write=4.385 s, sync=0.051 s, total=4.447 s; sync files=30, longest=0.025 s, average=0.002 s; distance=147 kB, estimate=8066 kB; lsn=0/6CEBC50, redo lsn=0/6CEBC18
2025-05-28 21:56:49.267 CST [15484] LOG:  checkpoint starting: time
2025-05-28 21:56:52.561 CST [15484] LOG:  checkpoint complete: wrote 33 buffers (0.2%); 0 WAL file(s) added, 0 removed, 0 recycled; write=3.266 s, sync=0.018 s, total=3.294 s; sync files=21, longest=0.002 s, average=0.001 s; distance=80 kB, estimate=7268 kB; lsn=0/6CFFC78, redo lsn=0/6CFFC40
2025-05-28 21:58:14.612 CST [34640] LOG:  received fast shutdown request
2025-05-28 21:58:14.614 CST [34640] LOG:  aborting any active transactions
2025-05-28 21:58:14.630 CST [34640] LOG:  background worker "logical replication launcher" (PID 35324) exited with exit code 1
2025-05-28 21:58:14.630 CST [15484] LOG:  shutting down
2025-05-28 21:58:14.632 CST [15484] LOG:  checkpoint starting: shutdown immediate
2025-05-28 21:58:14.642 CST [15484] LOG:  checkpoint complete: wrote 0 buffers (0.0%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.002 s, sync=0.001 s, total=0.012 s; sync files=0, longest=0.000 s, average=0.000 s; distance=0 kB, estimate=6541 kB; lsn=0/6CFFD28, redo lsn=0/6CFFD28
2025-05-28 21:58:14.675 CST [34640] LOG:  database system is shut down
2025-05-28 22:25:18.415 CST [544] LOG:  starting PostgreSQL 16.4, compiled by Visual C++ build 1940, 64-bit
2025-05-28 22:25:18.426 CST [544] LOG:  listening on IPv6 address "::1", port 5432
2025-05-28 22:25:18.426 CST [544] LOG:  listening on IPv4 address "127.0.0.1", port 5432
2025-05-28 22:25:19.162 CST [28592] LOG:  database system was shut down at 2025-05-28 21:58:14 CST
2025-05-28 22:25:19.775 CST [544] LOG:  database system is ready to accept connections
2025-05-28 22:30:18.773 CST [29448] LOG:  checkpoint starting: time
2025-05-28 22:31:19.847 CST [33564] LOG:  skipping analyze of "ir_module_category" --- lock not available
2025-05-28 22:31:19.847 CST [33564] LOG:  skipping vacuum of "ir_module_module" --- lock not available
2025-05-28 22:31:19.847 CST [33564] LOG:  skipping vacuum of "ir_module_module_dependency" --- lock not available
2025-05-28 22:31:19.847 CST [33564] LOG:  skipping vacuum of "ir_model_data" --- lock not available
2025-05-28 22:31:44.679 CST [29448] LOG:  checkpoint complete: wrote 788 buffers (4.8%); 0 WAL file(s) added, 0 removed, 1 recycled; write=85.434 s, sync=0.438 s, total=85.908 s; sync files=1048, longest=0.002 s, average=0.001 s; distance=6370 kB, estimate=6370 kB; lsn=0/97C3308, redo lsn=0/7338538
2025-05-28 22:35:18.694 CST [29448] LOG:  checkpoint starting: time
2025-05-28 22:35:20.277 CST [10072] LOG:  skipping analyze of "ir_act_server" --- lock not available
2025-05-28 22:35:20.378 CST [10072] LOG:  skipping vacuum of "res_partner" --- lock not available
2025-05-28 22:38:20.949 CST [32356] LOG:  skipping analyze of "res_partner" --- lock not available
2025-05-28 22:38:20.949 CST [32356] LOG:  skipping analyze of "res_users" --- lock not available
2025-05-28 22:38:21.134 CST [32356] LOG:  skipping analyze of "ir_act_window" --- lock not available
2025-05-28 22:38:21.141 CST [32356] LOG:  skipping analyze of "ir_attachment" --- lock not available
2025-05-28 22:38:21.141 CST [32356] LOG:  skipping analyze of "mail_message" --- lock not available
2025-05-28 22:38:21.141 CST [32356] LOG:  skipping analyze of "mail_template" --- lock not available
2025-05-28 22:38:21.199 CST [32356] LOG:  skipping vacuum of "product_template" --- lock not available
2025-05-28 22:38:21.199 CST [32356] LOG:  skipping vacuum of "product_product" --- lock not available
2025-05-28 22:39:20.677 CST [28732] LOG:  skipping analyze of "res_users" --- lock not available
2025-05-28 22:39:20.969 CST [28732] LOG:  skipping analyze of "ir_attachment" --- lock not available
2025-05-28 22:39:21.019 CST [28732] LOG:  skipping vacuum of "res_partner" --- lock not available
2025-05-28 22:39:22.374 CST [29072] ERROR:  null value in column "journal_id" of relation "account_move" violates not-null constraint
2025-05-28 22:39:22.374 CST [29072] DETAIL:  Failing row contains (1, null, null, null, 1, null, null, null, null, null, null, null, 9, null, 9, null, null, null, null, null, 6, null, null, 1, 1, null, null, null, null, draft, out_invoice, no, null, null, null, null, null, null, null, null, 2025-01-01, null, 2025-01-01, null, null, null, null, 1.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 2025-05-28 14:39:19.288188, 2025-05-28 14:39:19.288188, null, null).
2025-05-28 22:39:22.374 CST [29072] STATEMENT:  INSERT INTO "account_move" ("auto_post", "company_id", "create_date", "create_uid", "currency_id", "date", "delivery_date", "fiscal_position_id", "invoice_currency_rate", "invoice_date", "invoice_incoterm_id", "invoice_payment_term_id", "invoice_user_id", "journal_id", "move_type", "partner_id", "partner_shipping_id", "state", "write_date", "write_uid") VALUES ('no', 1, '2025-05-28 14:39:19.288188', 1, NULL, '2025-01-01', NULL, NULL, 1.0, '2025-01-01', NULL, NULL, 6, NULL, 'out_invoice', 9, 9, 'draft', '2025-05-28 14:39:19.288188', 1) RETURNING "id"
2025-05-28 22:39:31.939 CST [29072] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-28 22:39:31.939 CST [29072] DETAIL:  Key (name, journal_id)=(BNK1/2025/00004, 6) already exists.
2025-05-28 22:39:31.939 CST [29072] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (22, 'BNK1/2025/00004', '2025-05-28T14:39:27.430414'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-28 22:39:31.949 CST [29072] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-28 22:39:31.949 CST [29072] DETAIL:  Key (name, journal_id)=(BNK1/2025/00004, 6) already exists.
2025-05-28 22:39:31.949 CST [29072] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (23, 'BNK1/2025/00004', '2025-05-28T14:39:27.430414'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-28 22:39:31.951 CST [29072] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-28 22:39:31.951 CST [29072] DETAIL:  Key (name, journal_id)=(BNK1/2025/00005, 6) already exists.
2025-05-28 22:39:31.951 CST [29072] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (23, 'BNK1/2025/00005', '2025-05-28T14:39:27.430414'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-28 22:39:31.961 CST [29072] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-28 22:39:31.961 CST [29072] DETAIL:  Key (name, journal_id)=(BNK1/2025/00004, 6) already exists.
2025-05-28 22:39:31.961 CST [29072] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (24, 'BNK1/2025/00004', '2025-05-28T14:39:27.430414'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-28 22:39:31.964 CST [29072] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-28 22:39:31.964 CST [29072] DETAIL:  Key (name, journal_id)=(BNK1/2025/00005, 6) already exists.
2025-05-28 22:39:31.964 CST [29072] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (24, 'BNK1/2025/00005', '2025-05-28T14:39:27.430414'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-28 22:39:31.967 CST [29072] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-28 22:39:31.967 CST [29072] DETAIL:  Key (name, journal_id)=(BNK1/2025/00006, 6) already exists.
2025-05-28 22:39:31.967 CST [29072] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (24, 'BNK1/2025/00006', '2025-05-28T14:39:27.430414'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-28 22:39:31.976 CST [29072] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-28 22:39:31.976 CST [29072] DETAIL:  Key (name, journal_id)=(BNK1/2025/00004, 6) already exists.
2025-05-28 22:39:31.976 CST [29072] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (25, 'BNK1/2025/00004', '2025-05-28T14:39:27.430414'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-28 22:39:31.977 CST [29072] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-28 22:39:31.977 CST [29072] DETAIL:  Key (name, journal_id)=(BNK1/2025/00005, 6) already exists.
2025-05-28 22:39:31.977 CST [29072] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (25, 'BNK1/2025/00005', '2025-05-28T14:39:27.430414'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-28 22:39:31.979 CST [29072] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-28 22:39:31.979 CST [29072] DETAIL:  Key (name, journal_id)=(BNK1/2025/00006, 6) already exists.
2025-05-28 22:39:31.979 CST [29072] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (25, 'BNK1/2025/00006', '2025-05-28T14:39:27.430414'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-28 22:39:31.981 CST [29072] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-28 22:39:31.981 CST [29072] DETAIL:  Key (name, journal_id)=(BNK1/2025/00007, 6) already exists.
2025-05-28 22:39:31.981 CST [29072] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (25, 'BNK1/2025/00007', '2025-05-28T14:39:27.430414'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-28 22:39:49.101 CST [29448] LOG:  checkpoint complete: wrote 4397 buffers (26.8%); 0 WAL file(s) added, 0 removed, 3 recycled; write=269.364 s, sync=1.019 s, total=270.408 s; sync files=2260, longest=0.030 s, average=0.001 s; distance=48538 kB, estimate=48538 kB; lsn=0/CB49788, redo lsn=0/A29EE08
2025-05-28 22:40:18.150 CST [29448] LOG:  checkpoint starting: time
2025-05-28 22:40:20.156 CST [28912] LOG:  skipping vacuum of "account_move" --- lock not available
2025-05-28 22:40:20.262 CST [28912] LOG:  skipping analyze of "res_users" --- lock not available
2025-05-28 22:40:20.313 CST [28912] LOG:  skipping vacuum of "res_partner" --- lock not available
2025-05-28 22:40:20.471 CST [28912] LOG:  skipping vacuum of "account_move_line" --- lock not available
2025-05-28 22:44:48.680 CST [29448] LOG:  checkpoint complete: wrote 4505 buffers (27.5%); 0 WAL file(s) added, 0 removed, 2 recycled; write=269.835 s, sync=0.651 s, total=270.531 s; sync files=1232, longest=0.027 s, average=0.001 s; distance=45971 kB, estimate=48281 kB; lsn=0/DA262D8, redo lsn=0/CF83D78
2025-05-28 22:45:18.744 CST [29448] LOG:  checkpoint starting: time
2025-05-28 22:46:53.116 CST [29448] LOG:  checkpoint complete: wrote 868 buffers (5.3%); 0 WAL file(s) added, 0 removed, 1 recycled; write=94.191 s, sync=0.139 s, total=94.372 s; sync files=212, longest=0.028 s, average=0.001 s; distance=10890 kB, estimate=44542 kB; lsn=0/DA38330, redo lsn=0/DA26590
2025-05-28 22:50:18.140 CST [29448] LOG:  checkpoint starting: time
2025-05-28 22:50:21.712 CST [29448] LOG:  checkpoint complete: wrote 33 buffers (0.2%); 0 WAL file(s) added, 0 removed, 0 recycled; write=3.513 s, sync=0.022 s, total=3.572 s; sync files=30, longest=0.003 s, average=0.001 s; distance=85 kB, estimate=40096 kB; lsn=0/DA3BC08, redo lsn=0/DA3BBD0
2025-05-28 22:55:18.720 CST [29448] LOG:  checkpoint starting: time
2025-05-28 22:57:22.201 CST [27120] LOG:  skipping analyze of "ir_attachment" --- lock not available
2025-05-28 22:57:22.201 CST [27120] LOG:  skipping analyze of "mail_alias" --- lock not available
2025-05-28 22:57:22.201 CST [27120] LOG:  skipping analyze of "mail_message" --- lock not available
2025-05-28 22:57:22.201 CST [27120] LOG:  skipping analyze of "mail_template" --- lock not available
2025-05-28 22:57:22.204 CST [27120] LOG:  skipping vacuum of "res_partner" --- lock not available
2025-05-28 22:57:22.204 CST [27120] LOG:  skipping analyze of "res_users" --- lock not available
2025-05-28 22:58:20.278 CST [25680] LOG:  skipping analyze of "ir_attachment" --- lock not available
2025-05-28 22:58:20.278 CST [25680] LOG:  skipping analyze of "mail_alias" --- lock not available
2025-05-28 22:58:20.278 CST [25680] LOG:  skipping analyze of "mail_message" --- lock not available
2025-05-28 22:58:20.279 CST [25680] LOG:  skipping analyze of "mail_template" --- lock not available
2025-05-28 22:58:20.279 CST [25680] LOG:  skipping vacuum of "res_partner" --- lock not available
2025-05-28 22:58:20.279 CST [25680] LOG:  skipping analyze of "res_users" --- lock not available
2025-05-28 22:58:25.242 CST [29448] LOG:  checkpoint complete: wrote 1702 buffers (10.4%); 0 WAL file(s) added, 0 removed, 1 recycled; write=185.850 s, sync=0.647 s, total=186.522 s; sync files=1032, longest=0.032 s, average=0.001 s; distance=11687 kB, estimate=37255 kB; lsn=0/FE05FD8, redo lsn=0/E5A59D8
2025-05-28 22:59:20.316 CST [7788] LOG:  skipping analyze of "ir_attachment" --- lock not available
2025-05-28 22:59:20.316 CST [7788] LOG:  skipping analyze of "mail_alias" --- lock not available
2025-05-28 22:59:20.316 CST [7788] LOG:  skipping analyze of "mail_message" --- lock not available
2025-05-28 22:59:20.316 CST [7788] LOG:  skipping analyze of "mail_template" --- lock not available
2025-05-28 22:59:20.316 CST [7788] LOG:  skipping vacuum of "res_partner" --- lock not available
2025-05-28 22:59:20.316 CST [7788] LOG:  skipping analyze of "res_users" --- lock not available
2025-05-28 23:00:18.279 CST [29448] LOG:  checkpoint starting: time
2025-05-28 23:00:20.401 CST [15556] LOG:  skipping analyze of "ir_attachment" --- lock not available
2025-05-28 23:00:20.401 CST [15556] LOG:  skipping analyze of "mail_alias" --- lock not available
2025-05-28 23:00:20.401 CST [15556] LOG:  skipping analyze of "mail_message" --- lock not available
2025-05-28 23:00:20.402 CST [15556] LOG:  skipping analyze of "mail_template" --- lock not available
2025-05-28 23:00:20.402 CST [15556] LOG:  skipping vacuum of "res_partner" --- lock not available
2025-05-28 23:00:20.402 CST [15556] LOG:  skipping analyze of "res_users" --- lock not available
2025-05-28 23:01:20.430 CST [4040] LOG:  skipping analyze of "ir_attachment" --- lock not available
2025-05-28 23:01:20.431 CST [4040] LOG:  skipping analyze of "mail_alias" --- lock not available
2025-05-28 23:01:20.431 CST [4040] LOG:  skipping analyze of "mail_message" --- lock not available
2025-05-28 23:01:20.431 CST [4040] LOG:  skipping analyze of "mail_template" --- lock not available
2025-05-28 23:01:20.431 CST [4040] LOG:  skipping vacuum of "res_partner" --- lock not available
2025-05-28 23:01:20.431 CST [4040] LOG:  skipping analyze of "res_users" --- lock not available
2025-05-28 23:03:20.551 CST [14176] LOG:  skipping vacuum of "hr_employee" --- lock not available
2025-05-28 23:04:20.608 CST [30092] LOG:  skipping analyze of "res_partner" --- lock not available
2025-05-28 23:04:48.719 CST [29448] LOG:  checkpoint complete: wrote 3159 buffers (19.3%); 0 WAL file(s) added, 0 removed, 1 recycled; write=269.733 s, sync=0.668 s, total=270.440 s; sync files=1623, longest=0.026 s, average=0.001 s; distance=25283 kB, estimate=36058 kB; lsn=0/133B5E00, redo lsn=0/FE56938
2025-05-28 23:05:18.744 CST [29448] LOG:  checkpoint starting: time
2025-05-28 23:05:22.134 CST [4752] LOG:  skipping vacuum of "project_task" --- lock not available
2025-05-28 23:05:22.134 CST [4752] LOG:  skipping analyze of "res_company" --- lock not available
2025-05-28 23:07:20.788 CST [18984] LOG:  skipping analyze of "mail_template" --- lock not available
2025-05-28 23:07:20.793 CST [18984] LOG:  skipping vacuum of "res_partner" --- lock not available
2025-05-28 23:08:20.512 CST [11304] LOG:  skipping analyze of "mail_template" --- lock not available
2025-05-28 23:08:20.512 CST [11304] LOG:  skipping vacuum of "res_partner" --- lock not available
2025-05-28 23:09:20.562 CST [25508] LOG:  skipping vacuum of "project_task" --- lock not available
2025-05-28 23:09:48.914 CST [29448] LOG:  checkpoint complete: wrote 5339 buffers (32.6%); 0 WAL file(s) added, 0 removed, 4 recycled; write=269.341 s, sync=0.803 s, total=270.171 s; sync files=1451, longest=0.027 s, average=0.001 s; distance=56827 kB, estimate=56827 kB; lsn=0/151587F8, redo lsn=0/135D55D0
2025-05-28 23:10:18.967 CST [29448] LOG:  checkpoint starting: time
2025-05-28 23:10:20.497 CST [23740] LOG:  skipping vacuum of "project_task" --- lock not available
2025-05-28 23:14:48.515 CST [29448] LOG:  checkpoint complete: wrote 3161 buffers (19.3%); 0 WAL file(s) added, 0 removed, 2 recycled; write=269.060 s, sync=0.463 s, total=269.548 s; sync files=718, longest=0.027 s, average=0.001 s; distance=28269 kB, estimate=53971 kB; lsn=0/1592D720, redo lsn=0/15170C68
2025-05-28 23:15:18.548 CST [29448] LOG:  checkpoint starting: time
2025-05-28 23:17:45.321 CST [29448] LOG:  checkpoint complete: wrote 1349 buffers (8.2%); 0 WAL file(s) added, 0 removed, 0 recycled; write=146.595 s, sync=0.142 s, total=146.773 s; sync files=245, longest=0.003 s, average=0.001 s; distance=8227 kB, estimate=49396 kB; lsn=0/15A5E2E8, redo lsn=0/159799A0
2025-05-28 23:20:18.345 CST [29448] LOG:  checkpoint starting: time
2025-05-28 23:20:48.054 CST [29448] LOG:  checkpoint complete: wrote 271 buffers (1.7%); 0 WAL file(s) added, 0 removed, 0 recycled; write=29.566 s, sync=0.118 s, total=29.710 s; sync files=121, longest=0.030 s, average=0.001 s; distance=1019 kB, estimate=44559 kB; lsn=0/15A78670, redo lsn=0/15A78638
2025-05-29 08:27:23.069 CST [29448] LOG:  checkpoint starting: time
2025-05-29 08:27:39.827 CST [29448] LOG:  checkpoint complete: wrote 157 buffers (1.0%); 0 WAL file(s) added, 0 removed, 0 recycled; write=16.728 s, sync=0.027 s, total=16.758 s; sync files=119, longest=0.003 s, average=0.001 s; distance=432 kB, estimate=40146 kB; lsn=0/15AEA108, redo lsn=0/15AE47C0
2025-05-29 08:32:23.831 CST [29448] LOG:  checkpoint starting: time
2025-05-29 08:32:32.888 CST [29448] LOG:  checkpoint complete: wrote 84 buffers (0.5%); 0 WAL file(s) added, 0 removed, 0 recycled; write=9.030 s, sync=0.021 s, total=9.057 s; sync files=72, longest=0.003 s, average=0.001 s; distance=237 kB, estimate=36155 kB; lsn=0/15B1FF70, redo lsn=0/15B1FF00
2025-05-29 08:37:23.900 CST [29448] LOG:  checkpoint starting: time
2025-05-29 08:37:43.954 CST [29448] LOG:  checkpoint complete: wrote 187 buffers (1.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=20.014 s, sync=0.036 s, total=20.055 s; sync files=96, longest=0.002 s, average=0.001 s; distance=701 kB, estimate=32610 kB; lsn=0/15BED600, redo lsn=0/15BCF4A0
2025-05-29 08:42:23.963 CST [29448] LOG:  checkpoint starting: time
2025-05-29 08:42:27.904 CST [29448] LOG:  checkpoint complete: wrote 39 buffers (0.2%); 0 WAL file(s) added, 0 removed, 0 recycled; write=3.928 s, sync=0.009 s, total=3.941 s; sync files=30, longest=0.002 s, average=0.001 s; distance=154 kB, estimate=29364 kB; lsn=0/15BF8A98, redo lsn=0/15BF5E08
2025-05-29 08:47:23.905 CST [29448] LOG:  checkpoint starting: time
2025-05-29 08:47:26.199 CST [29448] LOG:  checkpoint complete: wrote 22 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=2.285 s, sync=0.005 s, total=2.295 s; sync files=11, longest=0.003 s, average=0.001 s; distance=12 kB, estimate=26429 kB; lsn=0/15BF9200, redo lsn=0/15BF91C8
2025-05-29 08:52:23.202 CST [29448] LOG:  checkpoint starting: time
2025-05-29 08:52:25.812 CST [29448] LOG:  checkpoint complete: wrote 25 buffers (0.2%); 0 WAL file(s) added, 0 removed, 0 recycled; write=2.599 s, sync=0.006 s, total=2.610 s; sync files=16, longest=0.002 s, average=0.001 s; distance=48 kB, estimate=23791 kB; lsn=0/15C05548, redo lsn=0/15C05510
2025-05-29 08:57:23.826 CST [29448] LOG:  checkpoint starting: time
2025-05-29 08:57:38.306 CST [29448] LOG:  checkpoint complete: wrote 134 buffers (0.8%); 0 WAL file(s) added, 0 removed, 0 recycled; write=14.447 s, sync=0.028 s, total=14.481 s; sync files=70, longest=0.002 s, average=0.001 s; distance=539 kB, estimate=21466 kB; lsn=0/15C8C320, redo lsn=0/15C8C2E8
2025-05-29 09:02:23.314 CST [29448] LOG:  checkpoint starting: time
2025-05-29 09:02:28.993 CST [29448] LOG:  checkpoint complete: wrote 53 buffers (0.3%); 0 WAL file(s) added, 0 removed, 0 recycled; write=5.662 s, sync=0.014 s, total=5.680 s; sync files=50, longest=0.003 s, average=0.001 s; distance=150 kB, estimate=19334 kB; lsn=0/15CB1DD8, redo lsn=0/15CB1D68
2025-05-29 09:07:23.994 CST [29448] LOG:  checkpoint starting: time
2025-05-29 09:07:35.126 CST [29448] LOG:  checkpoint complete: wrote 103 buffers (0.6%); 0 WAL file(s) added, 0 removed, 0 recycled; write=11.094 s, sync=0.032 s, total=11.132 s; sync files=81, longest=0.003 s, average=0.001 s; distance=256 kB, estimate=17426 kB; lsn=0/15D097E0, redo lsn=0/15CF2128
2025-05-29 09:12:23.127 CST [29448] LOG:  checkpoint starting: time
2025-05-29 09:12:34.041 CST [29448] LOG:  checkpoint complete: wrote 101 buffers (0.6%); 0 WAL file(s) added, 0 removed, 0 recycled; write=10.891 s, sync=0.019 s, total=10.914 s; sync files=81, longest=0.001 s, average=0.001 s; distance=282 kB, estimate=15712 kB; lsn=0/15D38C98, redo lsn=0/15D38C60
2025-05-29 09:22:23.052 CST [29448] LOG:  checkpoint starting: time
2025-05-29 09:22:24.914 CST [29448] LOG:  checkpoint complete: wrote 18 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=1.849 s, sync=0.007 s, total=1.863 s; sync files=18, longest=0.003 s, average=0.001 s; distance=43 kB, estimate=14145 kB; lsn=0/15D43C28, redo lsn=0/15D43BF0
2025-05-29 09:27:23.919 CST [29448] LOG:  checkpoint starting: time
2025-05-29 09:27:29.162 CST [29448] LOG:  checkpoint complete: wrote 49 buffers (0.3%); 0 WAL file(s) added, 0 removed, 0 recycled; write=5.223 s, sync=0.017 s, total=5.244 s; sync files=48, longest=0.003 s, average=0.001 s; distance=151 kB, estimate=12746 kB; lsn=0/15D69B78, redo lsn=0/15D69B40
2025-05-29 09:32:23.178 CST [29448] LOG:  checkpoint starting: time
2025-05-29 09:32:26.351 CST [29448] LOG:  checkpoint complete: wrote 30 buffers (0.2%); 0 WAL file(s) added, 0 removed, 0 recycled; write=3.158 s, sync=0.011 s, total=3.174 s; sync files=15, longest=0.005 s, average=0.001 s; distance=158 kB, estimate=11487 kB; lsn=0/15D91680, redo lsn=0/15D91648
2025-05-29 09:37:23.358 CST [29448] LOG:  checkpoint starting: time
2025-05-29 09:37:24.234 CST [29448] LOG:  checkpoint complete: wrote 11 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.867 s, sync=0.005 s, total=0.877 s; sync files=10, longest=0.003 s, average=0.001 s; distance=38 kB, estimate=10342 kB; lsn=0/15D9B268, redo lsn=0/15D9B230
2025-05-29 09:42:23.239 CST [29448] LOG:  checkpoint starting: time
2025-05-29 09:42:24.020 CST [29448] LOG:  checkpoint complete: wrote 10 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.770 s, sync=0.006 s, total=0.781 s; sync files=10, longest=0.002 s, average=0.001 s; distance=28 kB, estimate=9311 kB; lsn=0/15DA2648, redo lsn=0/15DA2610
2025-05-29 10:16:54.792 CST [29448] LOG:  checkpoint starting: time
2025-05-29 10:16:56.196 CST [29448] LOG:  checkpoint complete: wrote 16 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=1.394 s, sync=0.006 s, total=1.405 s; sync files=15, longest=0.003 s, average=0.001 s; distance=44 kB, estimate=8384 kB; lsn=0/15DAD790, redo lsn=0/15DAD758
2025-05-29 10:36:54.217 CST [29448] LOG:  checkpoint starting: time
2025-05-29 10:36:55.091 CST [29448] LOG:  checkpoint complete: wrote 11 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.863 s, sync=0.005 s, total=0.874 s; sync files=10, longest=0.002 s, average=0.001 s; distance=40 kB, estimate=7550 kB; lsn=0/15DB7AC0, redo lsn=0/15DB7A88
2025-05-29 10:41:54.105 CST [29448] LOG:  checkpoint starting: time
2025-05-29 10:41:54.874 CST [29448] LOG:  checkpoint complete: wrote 10 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.761 s, sync=0.004 s, total=0.769 s; sync files=10, longest=0.002 s, average=0.001 s; distance=32 kB, estimate=6798 kB; lsn=0/15DBFB68, redo lsn=0/15DBFB30
2025-05-29 10:46:38.039 CST [38780] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.
	
2025-05-29 10:46:38.039 CST [19628] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.
	
2025-05-29 10:46:38.039 CST [20052] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.
	
2025-05-29 10:46:38.040 CST [29792] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.
	
2025-05-29 10:46:38.040 CST [6764] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.
	
2025-05-29 10:46:54.879 CST [29448] LOG:  checkpoint starting: time
2025-05-29 10:46:55.648 CST [29448] LOG:  checkpoint complete: wrote 8 buffers (0.0%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.761 s, sync=0.004 s, total=0.770 s; sync files=7, longest=0.002 s, average=0.001 s; distance=22 kB, estimate=6120 kB; lsn=0/15DC5728, redo lsn=0/15DC56F0
2025-05-29 10:51:54.662 CST [29448] LOG:  checkpoint starting: time
2025-05-29 10:51:58.603 CST [29448] LOG:  checkpoint complete: wrote 37 buffers (0.2%); 0 WAL file(s) added, 0 removed, 0 recycled; write=3.926 s, sync=0.010 s, total=3.941 s; sync files=34, longest=0.001 s, average=0.001 s; distance=55 kB, estimate=5514 kB; lsn=0/15DD3468, redo lsn=0/15DD3430
2025-05-29 10:56:54.608 CST [29448] LOG:  checkpoint starting: time
2025-05-29 10:57:43.357 CST [29448] LOG:  checkpoint complete: wrote 449 buffers (2.7%); 0 WAL file(s) added, 1 removed, 0 recycled; write=48.718 s, sync=0.023 s, total=48.750 s; sync files=88, longest=0.002 s, average=0.001 s; distance=4498 kB, estimate=5412 kB; lsn=0/1627E498, redo lsn=0/16237E28
2025-05-29 10:58:07.533 CST [36012] ERROR:  update or delete on table "product_product" violates foreign key constraint "stock_move_product_id_fkey" on table "stock_move"
2025-05-29 10:58:07.533 CST [36012] DETAIL:  Key (id)=(44) is still referenced from table "stock_move".
2025-05-29 10:58:07.533 CST [36012] STATEMENT:  DELETE FROM "product_product" WHERE id IN (44, 45)
2025-05-29 10:58:07.553 CST [36012] ERROR:  update or delete on table "product_product" violates foreign key constraint "stock_move_product_id_fkey" on table "stock_move"
2025-05-29 10:58:07.553 CST [36012] DETAIL:  Key (id)=(44) is still referenced from table "stock_move".
2025-05-29 10:58:07.553 CST [36012] STATEMENT:  DELETE FROM "product_product" WHERE id IN (44)
2025-05-29 10:58:07.580 CST [36012] ERROR:  update or delete on table "product_product" violates foreign key constraint "stock_move_product_id_fkey" on table "stock_move"
2025-05-29 10:58:07.580 CST [36012] DETAIL:  Key (id)=(45) is still referenced from table "stock_move".
2025-05-29 10:58:07.580 CST [36012] STATEMENT:  DELETE FROM "product_product" WHERE id IN (45)
2025-05-29 10:58:07.590 CST [36012] ERROR:  update or delete on table "product_template_attribute_value" violates foreign key constraint "product_variant_combination_product_template_attribute_val_fkey" on table "product_variant_combination"
2025-05-29 10:58:07.590 CST [36012] DETAIL:  Key (id)=(18) is still referenced from table "product_variant_combination".
2025-05-29 10:58:07.590 CST [36012] STATEMENT:  DELETE FROM "product_template_attribute_value" WHERE id IN (18)
2025-05-29 10:58:07.626 CST [36012] ERROR:  update or delete on table "product_product" violates foreign key constraint "stock_move_product_id_fkey" on table "stock_move"
2025-05-29 10:58:07.626 CST [36012] DETAIL:  Key (id)=(45) is still referenced from table "stock_move".
2025-05-29 10:58:07.626 CST [36012] STATEMENT:  DELETE FROM "product_product" WHERE id IN (44, 45)
2025-05-29 10:58:07.644 CST [36012] ERROR:  update or delete on table "product_product" violates foreign key constraint "stock_move_product_id_fkey" on table "stock_move"
2025-05-29 10:58:07.644 CST [36012] DETAIL:  Key (id)=(45) is still referenced from table "stock_move".
2025-05-29 10:58:07.644 CST [36012] STATEMENT:  DELETE FROM "product_product" WHERE id IN (45)
2025-05-29 10:58:07.664 CST [36012] ERROR:  update or delete on table "product_product" violates foreign key constraint "stock_move_product_id_fkey" on table "stock_move"
2025-05-29 10:58:07.664 CST [36012] DETAIL:  Key (id)=(44) is still referenced from table "stock_move".
2025-05-29 10:58:07.664 CST [36012] STATEMENT:  DELETE FROM "product_product" WHERE id IN (44)
2025-05-29 10:58:07.705 CST [36012] ERROR:  update or delete on table "product_product" violates foreign key constraint "stock_move_product_id_fkey" on table "stock_move"
2025-05-29 10:58:07.705 CST [36012] DETAIL:  Key (id)=(45) is still referenced from table "stock_move".
2025-05-29 10:58:07.705 CST [36012] STATEMENT:  DELETE FROM "product_product" WHERE id IN (44, 45)
2025-05-29 10:58:07.721 CST [36012] ERROR:  update or delete on table "product_product" violates foreign key constraint "stock_move_product_id_fkey" on table "stock_move"
2025-05-29 10:58:07.721 CST [36012] DETAIL:  Key (id)=(45) is still referenced from table "stock_move".
2025-05-29 10:58:07.721 CST [36012] STATEMENT:  DELETE FROM "product_product" WHERE id IN (45)
2025-05-29 10:58:07.740 CST [36012] ERROR:  update or delete on table "product_product" violates foreign key constraint "stock_move_product_id_fkey" on table "stock_move"
2025-05-29 10:58:07.740 CST [36012] DETAIL:  Key (id)=(44) is still referenced from table "stock_move".
2025-05-29 10:58:07.740 CST [36012] STATEMENT:  DELETE FROM "product_product" WHERE id IN (44)
2025-05-29 10:58:08.752 CST [36012] ERROR:  update or delete on table "product_product" violates foreign key constraint "stock_move_product_id_fkey" on table "stock_move"
2025-05-29 10:58:08.752 CST [36012] DETAIL:  Key (id)=(45) is still referenced from table "stock_move".
2025-05-29 10:58:08.752 CST [36012] STATEMENT:  DELETE FROM "product_product" WHERE id IN (44, 45)
2025-05-29 10:58:08.773 CST [36012] ERROR:  update or delete on table "product_product" violates foreign key constraint "stock_move_product_id_fkey" on table "stock_move"
2025-05-29 10:58:08.773 CST [36012] DETAIL:  Key (id)=(45) is still referenced from table "stock_move".
2025-05-29 10:58:08.773 CST [36012] STATEMENT:  DELETE FROM "product_product" WHERE id IN (45)
2025-05-29 10:58:08.792 CST [36012] ERROR:  update or delete on table "product_product" violates foreign key constraint "stock_move_product_id_fkey" on table "stock_move"
2025-05-29 10:58:08.792 CST [36012] DETAIL:  Key (id)=(44) is still referenced from table "stock_move".
2025-05-29 10:58:08.792 CST [36012] STATEMENT:  DELETE FROM "product_product" WHERE id IN (44)
2025-05-29 10:58:08.921 CST [36012] ERROR:  update or delete on table "product_product" violates foreign key constraint "stock_move_product_id_fkey" on table "stock_move"
2025-05-29 10:58:08.921 CST [36012] DETAIL:  Key (id)=(45) is still referenced from table "stock_move".
2025-05-29 10:58:08.921 CST [36012] STATEMENT:  DELETE FROM "product_product" WHERE id IN (44, 45)
2025-05-29 10:58:08.944 CST [36012] ERROR:  update or delete on table "product_product" violates foreign key constraint "stock_move_product_id_fkey" on table "stock_move"
2025-05-29 10:58:08.944 CST [36012] DETAIL:  Key (id)=(45) is still referenced from table "stock_move".
2025-05-29 10:58:08.944 CST [36012] STATEMENT:  DELETE FROM "product_product" WHERE id IN (45)
2025-05-29 10:58:08.971 CST [36012] ERROR:  update or delete on table "product_product" violates foreign key constraint "stock_move_product_id_fkey" on table "stock_move"
2025-05-29 10:58:08.971 CST [36012] DETAIL:  Key (id)=(44) is still referenced from table "stock_move".
2025-05-29 10:58:08.971 CST [36012] STATEMENT:  DELETE FROM "product_product" WHERE id IN (44)
2025-05-29 10:58:09.036 CST [36012] ERROR:  update or delete on table "product_product" violates foreign key constraint "stock_move_product_id_fkey" on table "stock_move"
2025-05-29 10:58:09.036 CST [36012] DETAIL:  Key (id)=(45) is still referenced from table "stock_move".
2025-05-29 10:58:09.036 CST [36012] STATEMENT:  DELETE FROM "product_product" WHERE id IN (44, 45)
2025-05-29 10:58:09.063 CST [36012] ERROR:  update or delete on table "product_product" violates foreign key constraint "stock_move_product_id_fkey" on table "stock_move"
2025-05-29 10:58:09.063 CST [36012] DETAIL:  Key (id)=(45) is still referenced from table "stock_move".
2025-05-29 10:58:09.063 CST [36012] STATEMENT:  DELETE FROM "product_product" WHERE id IN (45)
2025-05-29 10:58:09.088 CST [36012] ERROR:  update or delete on table "product_product" violates foreign key constraint "stock_move_product_id_fkey" on table "stock_move"
2025-05-29 10:58:09.088 CST [36012] DETAIL:  Key (id)=(44) is still referenced from table "stock_move".
2025-05-29 10:58:09.088 CST [36012] STATEMENT:  DELETE FROM "product_product" WHERE id IN (44)
2025-05-29 10:58:09.156 CST [36012] ERROR:  update or delete on table "product_product" violates foreign key constraint "stock_move_product_id_fkey" on table "stock_move"
2025-05-29 10:58:09.156 CST [36012] DETAIL:  Key (id)=(45) is still referenced from table "stock_move".
2025-05-29 10:58:09.156 CST [36012] STATEMENT:  DELETE FROM "product_product" WHERE id IN (44, 45)
2025-05-29 10:58:09.176 CST [36012] ERROR:  update or delete on table "product_product" violates foreign key constraint "stock_move_product_id_fkey" on table "stock_move"
2025-05-29 10:58:09.176 CST [36012] DETAIL:  Key (id)=(45) is still referenced from table "stock_move".
2025-05-29 10:58:09.176 CST [36012] STATEMENT:  DELETE FROM "product_product" WHERE id IN (45)
2025-05-29 10:58:09.196 CST [36012] ERROR:  update or delete on table "product_product" violates foreign key constraint "stock_move_product_id_fkey" on table "stock_move"
2025-05-29 10:58:09.196 CST [36012] DETAIL:  Key (id)=(44) is still referenced from table "stock_move".
2025-05-29 10:58:09.196 CST [36012] STATEMENT:  DELETE FROM "product_product" WHERE id IN (44)
2025-05-29 10:58:09.891 CST [36012] ERROR:  new row for relation "sale_order_template_line" violates check constraint "sale_order_template_line_accountable_product_id_required"
2025-05-29 10:58:09.891 CST [36012] DETAIL:  Failing row contains (3, 1, 10, 1, null, 1, 1, 1, null, null, 1.00, 2025-05-28 14:40:41.152514, 2025-05-28 14:40:41.152514).
2025-05-29 10:58:09.891 CST [36012] CONTEXT:  SQL statement "UPDATE ONLY "public"."sale_order_template_line" SET "product_id" = NULL WHERE $1 OPERATOR(pg_catalog.=) "product_id""
2025-05-29 10:58:09.891 CST [36012] STATEMENT:  DELETE FROM "product_product" WHERE id IN (31)
2025-05-29 10:58:10.014 CST [36012] ERROR:  new row for relation "sale_order_template_line" violates check constraint "sale_order_template_line_accountable_product_id_required"
2025-05-29 10:58:10.014 CST [36012] DETAIL:  Failing row contains (3, 1, 10, 1, null, 1, 1, 1, null, null, 1.00, 2025-05-28 14:40:41.152514, 2025-05-28 14:40:41.152514).
2025-05-29 10:58:10.014 CST [36012] CONTEXT:  SQL statement "UPDATE ONLY "public"."sale_order_template_line" SET "product_id" = NULL WHERE $1 OPERATOR(pg_catalog.=) "product_id""
2025-05-29 10:58:10.014 CST [36012] STATEMENT:  DELETE FROM "product_product" WHERE id IN (31)
2025-05-29 10:59:37.316 CST [36012] ERROR:  new row for relation "account_move_line" violates check constraint "account_move_line_check_accountable_required_fields"
2025-05-29 10:59:37.316 CST [36012] DETAIL:  Failing row contains (68, 30, 8, 1, 6, 100, null, 6, 9, null, null, null, null, null, null, null, null, null, null, null, 2, 2, null, null, null, null, null, product, null, null, null, null, null, 0.00, 0.00, 0.00, 0.00, null, null, null, 1.00, 1200.00, null, null, 0.00, null, null, null, null, null, 2025-05-29 02:57:57.562065, 2025-05-29 02:57:57.562065, 2025-01-01, 2025-12-31, null, null, null, null).
2025-05-29 10:59:37.316 CST [36012] STATEMENT:  INSERT INTO "account_move_line" ("account_id", "amount_currency", "balance", "company_currency_id", "company_id", "create_date", "create_uid", "credit", "currency_id", "debit", "deferred_end_date", "deferred_start_date", "discount", "display_type", "journal_id", "move_id", "name", "partner_id", "price_unit", "product_uom_id", "quantity", "sequence", "tax_group_id", "tax_line_id", "write_date", "write_uid") VALUES (NULL, '0.00', '0.00', 6, 1, '2025-05-29 02:57:57.562065', 2, '0.00', 6, '0.00', '2025-12-31', '2025-01-01', '0.00', 'product', 8, 30, NULL, 9, '1200.00', NULL, '1.00', 100, NULL, NULL, '2025-05-29 02:57:57.562065', 2) RETURNING "id"
2025-05-29 11:01:54.362 CST [29448] LOG:  checkpoint starting: time
2025-05-29 11:05:11.953 CST [29448] LOG:  checkpoint complete: wrote 1812 buffers (11.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=197.415 s, sync=0.173 s, total=197.591 s; sync files=795, longest=0.003 s, average=0.001 s; distance=9084 kB, estimate=9084 kB; lsn=0/1705C5E0, redo lsn=0/16B170C8
2025-05-29 11:21:54.975 CST [29448] LOG:  checkpoint starting: time
2025-05-29 11:23:48.825 CST [29448] LOG:  checkpoint complete: wrote 1052 buffers (6.4%); 0 WAL file(s) added, 1 removed, 0 recycled; write=113.810 s, sync=0.032 s, total=113.851 s; sync files=136, longest=0.002 s, average=0.001 s; distance=5442 kB, estimate=8720 kB; lsn=0/170B7ED0, redo lsn=0/17067C50
2025-05-29 11:26:54.832 CST [29448] LOG:  checkpoint starting: time
2025-05-29 11:27:06.442 CST [36012] ERROR:  update or delete on table "res_company" violates foreign key constraint "helpdesk_team_company_id_fkey" on table "helpdesk_team"
2025-05-29 11:27:06.442 CST [36012] DETAIL:  Key (id)=(3) is still referenced from table "helpdesk_team".
2025-05-29 11:27:06.442 CST [36012] STATEMENT:  DELETE FROM "res_company" WHERE id IN (3, 1)
2025-05-29 11:27:17.116 CST [29448] LOG:  checkpoint complete: wrote 207 buffers (1.3%); 0 WAL file(s) added, 0 removed, 0 recycled; write=22.246 s, sync=0.034 s, total=22.285 s; sync files=109, longest=0.002 s, average=0.001 s; distance=841 kB, estimate=7932 kB; lsn=0/17182FE0, redo lsn=0/1713A200
2025-05-29 11:28:50.574 CST [36012] ERROR:  update or delete on table "res_company" violates foreign key constraint "helpdesk_team_company_id_fkey" on table "helpdesk_team"
2025-05-29 11:28:50.574 CST [36012] DETAIL:  Key (id)=(3) is still referenced from table "helpdesk_team".
2025-05-29 11:28:50.574 CST [36012] STATEMENT:  DELETE FROM "res_company" WHERE id IN (3, 1)
2025-05-29 11:30:46.230 CST [2300] ERROR:  update or delete on table "helpdesk_stage" violates foreign key constraint "helpdesk_ticket_stage_id_fkey" on table "helpdesk_ticket"
2025-05-29 11:30:46.230 CST [2300] DETAIL:  Key (id)=(1) is still referenced from table "helpdesk_ticket".
2025-05-29 11:30:46.230 CST [2300] STATEMENT:  DELETE FROM "helpdesk_stage" WHERE id IN (5, 4, 3, 2, 1)
2025-05-29 11:30:46.239 CST [2300] ERROR:  update or delete on table "helpdesk_stage" violates foreign key constraint "helpdesk_ticket_stage_id_fkey" on table "helpdesk_ticket"
2025-05-29 11:30:46.239 CST [2300] DETAIL:  Key (id)=(1) is still referenced from table "helpdesk_ticket".
2025-05-29 11:30:46.239 CST [2300] STATEMENT:  DELETE FROM "helpdesk_stage" WHERE id IN (3, 2, 1)
2025-05-29 11:30:46.245 CST [2300] ERROR:  update or delete on table "helpdesk_stage" violates foreign key constraint "helpdesk_ticket_stage_id_fkey" on table "helpdesk_ticket"
2025-05-29 11:30:46.245 CST [2300] DETAIL:  Key (id)=(1) is still referenced from table "helpdesk_ticket".
2025-05-29 11:30:46.245 CST [2300] STATEMENT:  DELETE FROM "helpdesk_stage" WHERE id IN (2, 1)
2025-05-29 11:30:46.249 CST [2300] ERROR:  update or delete on table "helpdesk_stage" violates foreign key constraint "helpdesk_ticket_stage_id_fkey" on table "helpdesk_ticket"
2025-05-29 11:30:46.249 CST [2300] DETAIL:  Key (id)=(1) is still referenced from table "helpdesk_ticket".
2025-05-29 11:30:46.249 CST [2300] STATEMENT:  DELETE FROM "helpdesk_stage" WHERE id IN (1)
2025-05-29 11:30:47.540 CST [2300] ERROR:  relation "helpdesk_stage" does not exist at character 35
2025-05-29 11:30:47.540 CST [2300] STATEMENT:  SELECT "helpdesk_stage"."id" FROM "helpdesk_stage" WHERE "helpdesk_stage"."id" IN (1)
2025-05-29 11:31:54.130 CST [29448] LOG:  checkpoint starting: time
2025-05-29 11:32:07.904 CST [36012] ERROR:  update or delete on table "res_company" violates foreign key constraint "stock_picking_type_company_id_fkey" on table "stock_picking_type"
2025-05-29 11:32:07.904 CST [36012] DETAIL:  Key (id)=(3) is still referenced from table "stock_picking_type".
2025-05-29 11:32:07.904 CST [36012] STATEMENT:  DELETE FROM "res_company" WHERE id IN (3, 1)
2025-05-29 11:35:24.048 CST [29448] LOG:  checkpoint complete: wrote 1933 buffers (11.8%); 0 WAL file(s) added, 0 removed, 0 recycled; write=209.843 s, sync=0.060 s, total=209.918 s; sync files=246, longest=0.003 s, average=0.001 s; distance=10524 kB, estimate=10524 kB; lsn=0/17BE4BC8, redo lsn=0/17B81230
2025-05-29 11:36:54.057 CST [29448] LOG:  checkpoint starting: time
2025-05-29 11:37:08.438 CST [29448] LOG:  checkpoint complete: wrote 135 buffers (0.8%); 0 WAL file(s) added, 0 removed, 0 recycled; write=14.351 s, sync=0.025 s, total=14.381 s; sync files=67, longest=0.003 s, average=0.001 s; distance=577 kB, estimate=9529 kB; lsn=0/17C40440, redo lsn=0/17C11A08
2025-05-29 11:41:54.441 CST [29448] LOG:  checkpoint starting: time
2025-05-29 11:42:19.253 CST [29448] LOG:  checkpoint complete: wrote 231 buffers (1.4%); 0 WAL file(s) added, 0 removed, 0 recycled; write=24.760 s, sync=0.045 s, total=24.813 s; sync files=169, longest=0.002 s, average=0.001 s; distance=725 kB, estimate=8649 kB; lsn=0/17CC81C8, redo lsn=0/17CC6E58
2025-05-29 11:46:54.271 CST [29448] LOG:  checkpoint starting: time
2025-05-29 11:47:05.405 CST [29448] LOG:  checkpoint complete: wrote 103 buffers (0.6%); 0 WAL file(s) added, 0 removed, 0 recycled; write=11.110 s, sync=0.019 s, total=11.135 s; sync files=78, longest=0.002 s, average=0.001 s; distance=328 kB, estimate=7816 kB; lsn=0/17D191F0, redo lsn=0/17D191B8
2025-05-29 11:51:54.417 CST [29448] LOG:  checkpoint starting: time
2025-05-29 11:52:00.087 CST [29448] LOG:  checkpoint complete: wrote 53 buffers (0.3%); 0 WAL file(s) added, 0 removed, 0 recycled; write=5.645 s, sync=0.019 s, total=5.670 s; sync files=51, longest=0.002 s, average=0.001 s; distance=129 kB, estimate=7048 kB; lsn=0/17D39778, redo lsn=0/17D39740
2025-05-29 11:56:54.090 CST [29448] LOG:  checkpoint starting: time
2025-05-29 11:57:05.640 CST [29448] LOG:  checkpoint complete: wrote 107 buffers (0.7%); 0 WAL file(s) added, 0 removed, 0 recycled; write=11.524 s, sync=0.021 s, total=11.550 s; sync files=35, longest=0.002 s, average=0.001 s; distance=1121 kB, estimate=6455 kB; lsn=0/17FA1118, redo lsn=0/17E51CB8
2025-05-29 12:01:54.647 CST [29448] LOG:  checkpoint starting: time
2025-05-29 12:03:21.636 CST [29448] LOG:  checkpoint complete: wrote 801 buffers (4.9%); 0 WAL file(s) added, 0 removed, 1 recycled; write=86.918 s, sync=0.064 s, total=86.989 s; sync files=150, longest=0.005 s, average=0.001 s; distance=6972 kB, estimate=6972 kB; lsn=0/18829210, redo lsn=0/18520F80
2025-05-29 12:06:54.644 CST [29448] LOG:  checkpoint starting: time
2025-05-29 12:07:45.153 CST [29448] LOG:  checkpoint complete: wrote 467 buffers (2.9%); 0 WAL file(s) added, 0 removed, 0 recycled; write=50.419 s, sync=0.084 s, total=50.509 s; sync files=206, longest=0.003 s, average=0.001 s; distance=3357 kB, estimate=6611 kB; lsn=0/188685E0, redo lsn=0/188685A8
2025-05-29 12:27:28.909 CST [29448] LOG:  checkpoint starting: time
2025-05-29 12:27:30.007 CST [29448] LOG:  checkpoint complete: wrote 11 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=1.088 s, sync=0.005 s, total=1.098 s; sync files=9, longest=0.002 s, average=0.001 s; distance=15 kB, estimate=5951 kB; lsn=0/1886C4F8, redo lsn=0/1886C4C0
2025-05-29 12:37:28.035 CST [29448] LOG:  checkpoint starting: time
2025-05-29 12:37:30.441 CST [29448] LOG:  checkpoint complete: wrote 25 buffers (0.2%); 0 WAL file(s) added, 0 removed, 0 recycled; write=2.394 s, sync=0.007 s, total=2.407 s; sync files=15, longest=0.002 s, average=0.001 s; distance=79 kB, estimate=5364 kB; lsn=0/18880358, redo lsn=0/18880320
2025-05-29 12:42:28.444 CST [29448] LOG:  checkpoint starting: time
2025-05-29 12:42:30.217 CST [29448] LOG:  checkpoint complete: wrote 19 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=1.761 s, sync=0.007 s, total=1.773 s; sync files=14, longest=0.003 s, average=0.001 s; distance=83 kB, estimate=4836 kB; lsn=0/18895138, redo lsn=0/18895100
2025-05-29 14:04:55.608 CST [9720] ERROR:  could not serialize access due to concurrent update
2025-05-29 14:04:55.608 CST [9720] STATEMENT:  
	            WITH last_cron_progress AS (
	                SELECT id as progress_id, cron_id, timed_out_counter, done, remaining
	                FROM ir_cron_progress
	                WHERE cron_id = 30
	                ORDER BY id DESC
	                LIMIT 1
	            )
	            SELECT *
	            FROM ir_cron
	            LEFT JOIN last_cron_progress lcp ON lcp.cron_id = ir_cron.id
	            WHERE ir_cron.active = true
	              AND (nextcall <= (now() at time zone 'UTC')
	                OR EXISTS (
	                    SELECT cron_id
	                    FROM ir_cron_trigger
	                    WHERE call_at <= (now() at time zone 'UTC')
	                      AND cron_id = ir_cron.id
	                )
	              )
	              AND id = 30
	            ORDER BY priority
	            FOR NO KEY UPDATE SKIP LOCKED
	        
2025-05-29 14:07:08.838 CST [29448] LOG:  checkpoint starting: time
2025-05-29 14:07:11.910 CST [29448] LOG:  checkpoint complete: wrote 31 buffers (0.2%); 0 WAL file(s) added, 0 removed, 0 recycled; write=3.053 s, sync=0.013 s, total=3.073 s; sync files=24, longest=0.004 s, average=0.001 s; distance=72 kB, estimate=4359 kB; lsn=0/188A7248, redo lsn=0/188A7210
2025-05-29 14:17:08.931 CST [29448] LOG:  checkpoint starting: time
2025-05-29 14:18:04.851 CST [29448] LOG:  checkpoint complete: wrote 516 buffers (3.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=55.903 s, sync=0.011 s, total=55.920 s; sync files=38, longest=0.003 s, average=0.001 s; distance=3638 kB, estimate=4287 kB; lsn=0/1933FA68, redo lsn=0/18C34B00
2025-05-29 14:21:49.061 CST [27700] ERROR:  column discuss_channel.is_ai_conversation does not exist at character 1147
2025-05-29 14:21:49.061 CST [27700] STATEMENT:  SELECT "discuss_channel"."id", "discuss_channel"."name", "discuss_channel"."active", "discuss_channel"."channel_type", "discuss_channel"."default_display_mode", "discuss_channel"."description", "discuss_channel"."parent_channel_id", "discuss_channel"."from_message_id", "discuss_channel"."sfu_channel_uuid", "discuss_channel"."sfu_server_url", "discuss_channel"."last_interest_dt", "discuss_channel"."uuid", "discuss_channel"."group_public_id", "discuss_channel"."allow_public_upload", "discuss_channel"."create_uid", "discuss_channel"."create_date", "discuss_channel"."write_uid", "discuss_channel"."write_date", "discuss_channel"."is_private", "discuss_channel"."ai_partner_id", "discuss_channel"."ext_ai_partner_id", "discuss_channel"."set_max_tokens", "discuss_channel"."set_chat_count", "discuss_channel"."set_temperature", "discuss_channel"."set_top_p", "discuss_channel"."set_frequency_penalty", "discuss_channel"."set_presence_penalty", "discuss_channel"."max_tokens", "discuss_channel"."chat_count", "discuss_channel"."temperature", "discuss_channel"."top_p", "discuss_channel"."frequency_penalty", "discuss_channel"."presence_penalty", "discuss_channel"."is_ai_conversation", "discuss_channel"."ai_sys_content", "discuss_channel"."ext_ai_sys_content" FROM "discuss_channel" WHERE ("discuss_channel"."id" IN (1, 2, 6, 7, 8, 9, 11))
2025-05-29 14:22:08.863 CST [29448] LOG:  checkpoint starting: time
2025-05-29 14:23:55.323 CST [29448] LOG:  checkpoint complete: wrote 982 buffers (6.0%); 0 WAL file(s) added, 0 removed, 1 recycled; write=106.418 s, sync=0.036 s, total=106.461 s; sync files=157, longest=0.002 s, average=0.001 s; distance=10708 kB, estimate=10708 kB; lsn=0/19DABBE8, redo lsn=0/196A9DC8
2025-05-29 14:27:08.324 CST [29448] LOG:  checkpoint starting: time
2025-05-29 14:28:38.941 CST [29448] LOG:  checkpoint complete: wrote 838 buffers (5.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=90.587 s, sync=0.026 s, total=90.617 s; sync files=105, longest=0.002 s, average=0.001 s; distance=7213 kB, estimate=10359 kB; lsn=0/19DB5218, redo lsn=0/19DB51E0
2025-05-29 14:32:55.329 CST [29448] LOG:  checkpoint starting: time
2025-05-29 14:32:55.878 CST [29448] LOG:  checkpoint complete: wrote 6 buffers (0.0%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.540 s, sync=0.005 s, total=0.550 s; sync files=6, longest=0.002 s, average=0.001 s; distance=10 kB, estimate=9324 kB; lsn=0/19DB7DE0, redo lsn=0/19DB7DA8
2025-05-29 14:37:55.889 CST [29448] LOG:  checkpoint starting: time
2025-05-29 14:37:56.882 CST [29448] LOG:  checkpoint complete: wrote 12 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.982 s, sync=0.007 s, total=0.994 s; sync files=11, longest=0.003 s, average=0.001 s; distance=39 kB, estimate=8395 kB; lsn=0/19DC1A10, redo lsn=0/19DC19D8
2025-05-29 14:42:55.898 CST [29448] LOG:  checkpoint starting: time
2025-05-29 14:42:56.686 CST [29448] LOG:  checkpoint complete: wrote 10 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.777 s, sync=0.005 s, total=0.788 s; sync files=10, longest=0.003 s, average=0.001 s; distance=20 kB, estimate=7558 kB; lsn=0/19DC6C60, redo lsn=0/19DC6C28
2025-05-29 14:57:55.717 CST [29448] LOG:  checkpoint starting: time
2025-05-29 14:57:55.829 CST [29448] LOG:  checkpoint complete: wrote 2 buffers (0.0%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.106 s, sync=0.003 s, total=0.113 s; sync files=2, longest=0.002 s, average=0.002 s; distance=3 kB, estimate=6802 kB; lsn=0/19DC78B8, redo lsn=0/19DC7880
2025-05-29 15:29:55.213 CST [29448] LOG:  checkpoint starting: time
2025-05-29 15:29:56.103 CST [29448] LOG:  checkpoint complete: wrote 9 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.878 s, sync=0.005 s, total=0.891 s; sync files=7, longest=0.003 s, average=0.001 s; distance=11 kB, estimate=6123 kB; lsn=0/19DCA6F8, redo lsn=0/19DCA6C0
2025-05-29 15:54:56.179 CST [28576] ERROR:  could not serialize access due to concurrent update
2025-05-29 15:54:56.179 CST [28576] STATEMENT:  
	            WITH last_cron_progress AS (
	                SELECT id as progress_id, cron_id, timed_out_counter, done, remaining
	                FROM ir_cron_progress
	                WHERE cron_id = 30
	                ORDER BY id DESC
	                LIMIT 1
	            )
	            SELECT *
	            FROM ir_cron
	            LEFT JOIN last_cron_progress lcp ON lcp.cron_id = ir_cron.id
	            WHERE ir_cron.active = true
	              AND (nextcall <= (now() at time zone 'UTC')
	                OR EXISTS (
	                    SELECT cron_id
	                    FROM ir_cron_trigger
	                    WHERE call_at <= (now() at time zone 'UTC')
	                      AND cron_id = ir_cron.id
	                )
	              )
	              AND id = 30
	            ORDER BY priority
	            FOR NO KEY UPDATE SKIP LOCKED
	        
2025-05-29 15:55:07.521 CST [29448] LOG:  checkpoint starting: time
2025-05-29 15:55:10.253 CST [29448] LOG:  checkpoint complete: wrote 28 buffers (0.2%); 0 WAL file(s) added, 0 removed, 0 recycled; write=2.719 s, sync=0.009 s, total=2.732 s; sync files=22, longest=0.001 s, average=0.001 s; distance=53 kB, estimate=5516 kB; lsn=0/19DD7CE0, redo lsn=0/19DD7CA8
2025-05-29 15:56:56.476 CST [28004] ERROR:  could not obtain lock on row in relation "ir_cron"
2025-05-29 15:56:56.476 CST [28004] STATEMENT:  SELECT * FROM ir_cron FOR UPDATE NOWAIT
2025-05-29 16:00:07.265 CST [29448] LOG:  checkpoint starting: time
2025-05-29 16:03:29.881 CST [29448] LOG:  checkpoint complete: wrote 1867 buffers (11.4%); 0 WAL file(s) added, 0 removed, 2 recycled; write=202.543 s, sync=0.067 s, total=202.617 s; sync files=241, longest=0.002 s, average=0.001 s; distance=19393 kB, estimate=19393 kB; lsn=0/1B0CD1C8, redo lsn=0/1B0C8298
2025-05-29 16:09:54.574 CST [29448] LOG:  checkpoint starting: immediate force wait
2025-05-29 16:09:54.585 CST [29448] LOG:  checkpoint complete: wrote 23 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.002 s, sync=0.004 s, total=0.011 s; sync files=7, longest=0.001 s, average=0.001 s; distance=4133 kB, estimate=17867 kB; lsn=0/1B4D18C8, redo lsn=0/1B4D1890
2025-05-29 16:14:30.053 CST [15580] FATAL:  database "odoo_ent" does not exist
2025-05-29 16:14:30.099 CST [38540] FATAL:  database "odoo_ent" does not exist
2025-05-29 16:14:54.591 CST [29448] LOG:  checkpoint starting: time
2025-05-29 16:15:53.181 CST [29448] LOG:  checkpoint complete: wrote 896 buffers (5.5%); 0 WAL file(s) added, 0 removed, 2 recycled; write=58.570 s, sync=0.011 s, total=58.590 s; sync files=24, longest=0.001 s, average=0.001 s; distance=39145 kB, estimate=39145 kB; lsn=0/1DB199C0, redo lsn=0/1DB0BE10
2025-05-29 16:15:53.186 CST [29448] LOG:  checkpoint starting: immediate force wait
2025-05-29 16:15:53.192 CST [29448] LOG:  checkpoint complete: wrote 5 buffers (0.0%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.001 s, sync=0.001 s, total=0.007 s; sync files=1, longest=0.001 s, average=0.001 s; distance=55 kB, estimate=35236 kB; lsn=0/1DB19A70, redo lsn=0/1DB19A38
2025-05-29 16:18:11.437 CST [7092] LOG:  skipping analyze of "ir_module_category" --- lock not available
2025-05-29 16:18:11.438 CST [7092] LOG:  skipping vacuum of "ir_module_module" --- lock not available
2025-05-29 16:18:11.438 CST [7092] LOG:  skipping vacuum of "ir_module_module_dependency" --- lock not available
2025-05-29 16:18:11.438 CST [7092] LOG:  skipping vacuum of "ir_model_data" --- lock not available
2025-05-29 16:20:53.203 CST [29448] LOG:  checkpoint starting: time
2025-05-29 16:25:23.234 CST [29448] LOG:  checkpoint complete: wrote 3834 buffers (23.4%); 0 WAL file(s) added, 0 removed, 3 recycled; write=269.851 s, sync=0.171 s, total=270.031 s; sync files=999, longest=0.002 s, average=0.001 s; distance=38499 kB, estimate=38499 kB; lsn=0/200B4C50, redo lsn=0/200B2808
2025-05-29 16:35:53.267 CST [29448] LOG:  checkpoint starting: time
2025-05-29 16:39:32.759 CST [29448] LOG:  checkpoint complete: wrote 2023 buffers (12.3%); 0 WAL file(s) added, 0 removed, 1 recycled; write=219.346 s, sync=0.142 s, total=219.493 s; sync files=772, longest=0.003 s, average=0.001 s; distance=17122 kB, estimate=36361 kB; lsn=0/2116B2D0, redo lsn=0/2116B298
2025-05-29 16:45:53.773 CST [29448] LOG:  checkpoint starting: time
2025-05-29 16:45:55.182 CST [29448] LOG:  checkpoint complete: wrote 14 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=1.399 s, sync=0.006 s, total=1.409 s; sync files=12, longest=0.003 s, average=0.001 s; distance=23 kB, estimate=32727 kB; lsn=0/********, redo lsn=0/********
2025-05-29 16:50:43.943 CST [1112] ERROR:  null value in column "journal_id" of relation "account_move" violates not-null constraint
2025-05-29 16:50:43.943 CST [1112] DETAIL:  Failing row contains (1, null, null, null, 1, null, null, null, null, null, null, null, 9, null, 9, null, null, null, null, null, 6, null, null, 1, 1, null, null, null, null, draft, out_invoice, no, null, null, null, null, null, null, null, null, 2025-01-01, null, 2025-01-01, null, null, null, null, 1.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 2025-05-29 08:50:42.428877, 2025-05-29 08:50:42.428877, null, null).
2025-05-29 16:50:43.943 CST [1112] STATEMENT:  INSERT INTO "account_move" ("auto_post", "company_id", "create_date", "create_uid", "currency_id", "date", "delivery_date", "fiscal_position_id", "invoice_currency_rate", "invoice_date", "invoice_incoterm_id", "invoice_payment_term_id", "invoice_user_id", "journal_id", "move_type", "partner_id", "partner_shipping_id", "state", "write_date", "write_uid") VALUES ('no', 1, '2025-05-29 08:50:42.428877', 1, NULL, '2025-01-01', NULL, NULL, 1.0, '2025-01-01', NULL, NULL, 6, NULL, 'out_invoice', 9, 9, 'draft', '2025-05-29 08:50:42.428877', 1) RETURNING "id"
2025-05-29 16:50:47.612 CST [1112] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-29 16:50:47.612 CST [1112] DETAIL:  Key (name, journal_id)=(BNK1/2025/00004, 6) already exists.
2025-05-29 16:50:47.612 CST [1112] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (22, 'BNK1/2025/00004', '2025-05-29T08:50:45.386839'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-29 16:50:47.618 CST [1112] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-29 16:50:47.618 CST [1112] DETAIL:  Key (name, journal_id)=(BNK1/2025/00004, 6) already exists.
2025-05-29 16:50:47.618 CST [1112] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (23, 'BNK1/2025/00004', '2025-05-29T08:50:45.386839'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-29 16:50:47.619 CST [1112] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-29 16:50:47.619 CST [1112] DETAIL:  Key (name, journal_id)=(BNK1/2025/00005, 6) already exists.
2025-05-29 16:50:47.619 CST [1112] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (23, 'BNK1/2025/00005', '2025-05-29T08:50:45.386839'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-29 16:50:47.625 CST [1112] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-29 16:50:47.625 CST [1112] DETAIL:  Key (name, journal_id)=(BNK1/2025/00004, 6) already exists.
2025-05-29 16:50:47.625 CST [1112] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (24, 'BNK1/2025/00004', '2025-05-29T08:50:45.386839'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-29 16:50:47.626 CST [1112] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-29 16:50:47.626 CST [1112] DETAIL:  Key (name, journal_id)=(BNK1/2025/00005, 6) already exists.
2025-05-29 16:50:47.626 CST [1112] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (24, 'BNK1/2025/00005', '2025-05-29T08:50:45.386839'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-29 16:50:47.627 CST [1112] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-29 16:50:47.627 CST [1112] DETAIL:  Key (name, journal_id)=(BNK1/2025/00006, 6) already exists.
2025-05-29 16:50:47.627 CST [1112] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (24, 'BNK1/2025/00006', '2025-05-29T08:50:45.386839'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-29 16:50:47.631 CST [1112] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-29 16:50:47.631 CST [1112] DETAIL:  Key (name, journal_id)=(BNK1/2025/00004, 6) already exists.
2025-05-29 16:50:47.631 CST [1112] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (25, 'BNK1/2025/00004', '2025-05-29T08:50:45.386839'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-29 16:50:47.632 CST [1112] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-29 16:50:47.632 CST [1112] DETAIL:  Key (name, journal_id)=(BNK1/2025/00005, 6) already exists.
2025-05-29 16:50:47.632 CST [1112] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (25, 'BNK1/2025/00005', '2025-05-29T08:50:45.386839'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-29 16:50:47.633 CST [1112] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-29 16:50:47.633 CST [1112] DETAIL:  Key (name, journal_id)=(BNK1/2025/00006, 6) already exists.
2025-05-29 16:50:47.633 CST [1112] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (25, 'BNK1/2025/00006', '2025-05-29T08:50:45.386839'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-29 16:50:47.634 CST [1112] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-29 16:50:47.634 CST [1112] DETAIL:  Key (name, journal_id)=(BNK1/2025/00007, 6) already exists.
2025-05-29 16:50:47.634 CST [1112] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (25, 'BNK1/2025/00007', '2025-05-29T08:50:45.386839'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-29 16:50:53.190 CST [29448] LOG:  checkpoint starting: time
2025-05-29 16:55:23.294 CST [29448] LOG:  checkpoint complete: wrote 3077 buffers (18.8%); 0 WAL file(s) added, 0 removed, 1 recycled; write=269.879 s, sync=0.218 s, total=270.105 s; sync files=1281, longest=0.003 s, average=0.001 s; distance=28886 kB, estimate=32343 kB; lsn=0/2377AC00, redo lsn=0/22DA6AD8
2025-05-29 17:00:53.325 CST [29448] LOG:  checkpoint starting: time
2025-05-29 17:02:27.830 CST [29448] LOG:  checkpoint complete: wrote 872 buffers (5.3%); 0 WAL file(s) added, 0 removed, 1 recycled; write=94.463 s, sync=0.036 s, total=94.505 s; sync files=161, longest=0.002 s, average=0.001 s; distance=10979 kB, estimate=30207 kB; lsn=0/23C18A68, redo lsn=0/2385F830
2025-05-29 17:05:53.845 CST [29448] LOG:  checkpoint starting: time
2025-05-29 17:10:23.154 CST [29448] LOG:  checkpoint complete: wrote 2576 buffers (15.7%); 0 WAL file(s) added, 0 removed, 2 recycled; write=269.200 s, sync=0.101 s, total=269.309 s; sync files=451, longest=0.002 s, average=0.001 s; distance=24986 kB, estimate=29685 kB; lsn=0/2592BD58, redo lsn=0/250C6038
2025-05-29 17:11:13.650 CST [15164] ERROR:  update or delete on table "res_company" violates foreign key constraint "account_journal_company_id_fkey" on table "account_journal"
2025-05-29 17:11:13.650 CST [15164] DETAIL:  Key (id)=(2) is still referenced from table "account_journal".
2025-05-29 17:11:13.650 CST [15164] STATEMENT:  DELETE FROM "res_company" WHERE id IN (2)
2025-05-29 17:11:34.159 CST [15164] ERROR:  update or delete on table "res_company" violates foreign key constraint "res_users_company_id_fkey" on table "res_users"
2025-05-29 17:11:34.159 CST [15164] DETAIL:  Key (id)=(1) is still referenced from table "res_users".
2025-05-29 17:11:34.159 CST [15164] STATEMENT:  DELETE FROM "res_company" WHERE id IN (1)
2025-05-29 17:15:53.178 CST [29448] LOG:  checkpoint starting: time
2025-05-29 17:17:23.227 CST [29448] LOG:  checkpoint complete: wrote 826 buffers (5.0%); 0 WAL file(s) added, 0 removed, 0 recycled; write=89.969 s, sync=0.075 s, total=90.050 s; sync files=203, longest=0.005 s, average=0.001 s; distance=9752 kB, estimate=27691 kB; lsn=0/25A4C348, redo lsn=0/25A4C310
2025-05-29 17:19:21.281 CST [15164] ERROR:  update or delete on table "res_company" violates foreign key constraint "account_journal_company_id_fkey" on table "account_journal"
2025-05-29 17:19:21.281 CST [15164] DETAIL:  Key (id)=(2) is still referenced from table "account_journal".
2025-05-29 17:19:21.281 CST [15164] STATEMENT:  DELETE FROM "res_company" WHERE id IN (2)
2025-05-29 17:20:53.239 CST [29448] LOG:  checkpoint starting: time
2025-05-29 17:20:57.740 CST [29448] LOG:  checkpoint complete: wrote 42 buffers (0.3%); 0 WAL file(s) added, 0 removed, 0 recycled; write=4.482 s, sync=0.013 s, total=4.502 s; sync files=43, longest=0.003 s, average=0.001 s; distance=165 kB, estimate=24939 kB; lsn=0/25DE8120, redo lsn=0/25A757D0
2025-05-29 17:25:53.744 CST [29448] LOG:  checkpoint starting: time
2025-05-29 17:27:04.250 CST [29448] LOG:  checkpoint complete: wrote 647 buffers (3.9%); 0 WAL file(s) added, 0 removed, 0 recycled; write=70.450 s, sync=0.052 s, total=70.507 s; sync files=182, longest=0.003 s, average=0.001 s; distance=4529 kB, estimate=22898 kB; lsn=0/264677D0, redo lsn=0/25EE1D50
2025-05-29 17:28:53.039 CST [15164] ERROR:  update or delete on table "res_company" violates foreign key constraint "account_journal_company_id_fkey" on table "account_journal"
2025-05-29 17:28:53.039 CST [15164] DETAIL:  Key (id)=(2) is still referenced from table "account_journal".
2025-05-29 17:28:53.039 CST [15164] STATEMENT:  DELETE FROM "res_company" WHERE id IN (2)
2025-05-29 17:30:53.266 CST [29448] LOG:  checkpoint starting: time
2025-05-29 17:32:15.083 CST [29448] LOG:  checkpoint complete: wrote 752 buffers (4.6%); 0 WAL file(s) added, 0 removed, 1 recycled; write=81.808 s, sync=0.003 s, total=81.818 s; sync files=6, longest=0.001 s, average=0.001 s; distance=9561 kB, estimate=21564 kB; lsn=0/2685A898, redo lsn=0/268383C0
2025-05-29 17:32:15.085 CST [29448] LOG:  checkpoint starting: immediate force wait
2025-05-29 17:32:15.091 CST [29448] LOG:  checkpoint complete: wrote 14 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.001 s, sync=0.003 s, total=0.006 s; sync files=6, longest=0.001 s, average=0.001 s; distance=137 kB, estimate=19421 kB; lsn=0/2685A948, redo lsn=0/2685A910
2025-05-29 17:32:52.705 CST [16724] LOG:  skipping analyze of "ir_module_category" --- lock not available
2025-05-29 17:32:52.705 CST [16724] LOG:  skipping vacuum of "ir_module_module" --- lock not available
2025-05-29 17:32:52.705 CST [16724] LOG:  skipping vacuum of "ir_module_module_dependency" --- lock not available
2025-05-29 17:32:52.705 CST [16724] LOG:  skipping vacuum of "ir_model_data" --- lock not available
2025-05-29 17:37:15.094 CST [29448] LOG:  checkpoint starting: time
2025-05-29 17:40:28.141 CST [28812] ERROR:  null value in column "journal_id" of relation "account_move" violates not-null constraint
2025-05-29 17:40:28.141 CST [28812] DETAIL:  Failing row contains (1, null, null, null, 1, null, null, null, null, null, null, null, 9, null, 9, null, null, null, null, null, 6, null, null, 1, 1, null, null, null, null, draft, out_invoice, no, null, null, null, null, null, null, null, null, 2025-01-01, null, 2025-01-01, null, null, null, null, 1.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 2025-05-29 09:40:26.871392, 2025-05-29 09:40:26.871392, null, null).
2025-05-29 17:40:28.141 CST [28812] STATEMENT:  INSERT INTO "account_move" ("auto_post", "company_id", "create_date", "create_uid", "currency_id", "date", "delivery_date", "fiscal_position_id", "invoice_currency_rate", "invoice_date", "invoice_incoterm_id", "invoice_payment_term_id", "invoice_user_id", "journal_id", "move_type", "partner_id", "partner_shipping_id", "state", "write_date", "write_uid") VALUES ('no', 1, '2025-05-29 09:40:26.871392', 1, NULL, '2025-01-01', NULL, NULL, 1.0, '2025-01-01', NULL, NULL, 6, NULL, 'out_invoice', 9, 9, 'draft', '2025-05-29 09:40:26.871392', 1) RETURNING "id"
2025-05-29 17:40:31.642 CST [28812] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-29 17:40:31.642 CST [28812] DETAIL:  Key (name, journal_id)=(BNK1/2025/00004, 6) already exists.
2025-05-29 17:40:31.642 CST [28812] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (22, 'BNK1/2025/00004', '2025-05-29T09:40:29.417819'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-29 17:40:31.647 CST [28812] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-29 17:40:31.647 CST [28812] DETAIL:  Key (name, journal_id)=(BNK1/2025/00004, 6) already exists.
2025-05-29 17:40:31.647 CST [28812] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (23, 'BNK1/2025/00004', '2025-05-29T09:40:29.417819'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-29 17:40:31.648 CST [28812] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-29 17:40:31.648 CST [28812] DETAIL:  Key (name, journal_id)=(BNK1/2025/00005, 6) already exists.
2025-05-29 17:40:31.648 CST [28812] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (23, 'BNK1/2025/00005', '2025-05-29T09:40:29.417819'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-29 17:40:31.654 CST [28812] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-29 17:40:31.654 CST [28812] DETAIL:  Key (name, journal_id)=(BNK1/2025/00004, 6) already exists.
2025-05-29 17:40:31.654 CST [28812] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (24, 'BNK1/2025/00004', '2025-05-29T09:40:29.417819'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-29 17:40:31.655 CST [28812] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-29 17:40:31.655 CST [28812] DETAIL:  Key (name, journal_id)=(BNK1/2025/00005, 6) already exists.
2025-05-29 17:40:31.655 CST [28812] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (24, 'BNK1/2025/00005', '2025-05-29T09:40:29.417819'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-29 17:40:31.656 CST [28812] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-29 17:40:31.656 CST [28812] DETAIL:  Key (name, journal_id)=(BNK1/2025/00006, 6) already exists.
2025-05-29 17:40:31.656 CST [28812] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (24, 'BNK1/2025/00006', '2025-05-29T09:40:29.417819'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-29 17:40:31.664 CST [28812] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-29 17:40:31.664 CST [28812] DETAIL:  Key (name, journal_id)=(BNK1/2025/00004, 6) already exists.
2025-05-29 17:40:31.664 CST [28812] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (25, 'BNK1/2025/00004', '2025-05-29T09:40:29.417819'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-29 17:40:31.665 CST [28812] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-29 17:40:31.665 CST [28812] DETAIL:  Key (name, journal_id)=(BNK1/2025/00005, 6) already exists.
2025-05-29 17:40:31.665 CST [28812] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (25, 'BNK1/2025/00005', '2025-05-29T09:40:29.417819'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-29 17:40:31.666 CST [28812] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-29 17:40:31.666 CST [28812] DETAIL:  Key (name, journal_id)=(BNK1/2025/00006, 6) already exists.
2025-05-29 17:40:31.666 CST [28812] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (25, 'BNK1/2025/00006', '2025-05-29T09:40:29.417819'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-29 17:40:31.667 CST [28812] ERROR:  duplicate key value violates unique constraint "account_move_unique_name"
2025-05-29 17:40:31.667 CST [28812] DETAIL:  Key (name, journal_id)=(BNK1/2025/00007, 6) already exists.
2025-05-29 17:40:31.667 CST [28812] STATEMENT:   UPDATE "account_move"
	                    SET "name" = "__tmp"."name"::VARCHAR, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (25, 'BNK1/2025/00007', '2025-05-29T09:40:29.417819'::timestamp, 1)) AS "__tmp"("id", "name", "write_date", "write_uid")
	                    WHERE "account_move"."id" = "__tmp"."id"
	                
2025-05-29 17:40:53.431 CST [35424] LOG:  skipping vacuum of "ir_model" --- lock not available
2025-05-29 17:40:54.367 CST [35424] LOG:  skipping vacuum of "account_account" --- lock not available
2025-05-29 17:40:54.368 CST [35424] LOG:  skipping vacuum of "account_move_line" --- lock not available
2025-05-29 17:40:54.368 CST [35424] LOG:  skipping vacuum of "account_move" --- lock not available
2025-05-29 17:41:45.405 CST [29448] LOG:  checkpoint complete: wrote 4653 buffers (28.4%); 0 WAL file(s) added, 0 removed, 3 recycled; write=269.965 s, sync=0.337 s, total=270.311 s; sync files=2558, longest=0.003 s, average=0.001 s; distance=52010 kB, estimate=52010 kB; lsn=0/2BF641D0, redo lsn=0/29B25330
2025-05-29 17:42:15.417 CST [29448] LOG:  checkpoint starting: time
2025-05-29 17:45:53.747 CST [25296] LOG:  skipping analyze of "account_account" --- lock not available
2025-05-29 17:46:45.221 CST [29448] LOG:  checkpoint complete: wrote 3670 buffers (22.4%); 0 WAL file(s) added, 0 removed, 2 recycled; write=269.646 s, sync=0.147 s, total=269.804 s; sync files=678, longest=0.002 s, average=0.001 s; distance=37624 kB, estimate=50571 kB; lsn=0/2D5E9CF8, redo lsn=0/2BFE3458
2025-05-29 17:47:15.228 CST [29448] LOG:  checkpoint starting: time
2025-05-29 17:47:53.125 CST [31792] LOG:  skipping vacuum of "ir_module_module" --- lock not available
2025-05-29 17:47:53.125 CST [31792] LOG:  skipping analyze of "ir_model" --- lock not available
2025-05-29 17:47:53.125 CST [31792] LOG:  skipping analyze of "ir_model_fields" --- lock not available
2025-05-29 17:47:53.133 CST [31792] LOG:  skipping analyze of "res_partner" --- lock not available
2025-05-29 17:51:10.863 CST [29448] LOG:  checkpoint complete: wrote 2160 buffers (13.2%); 0 WAL file(s) added, 0 removed, 2 recycled; write=235.518 s, sync=0.107 s, total=235.635 s; sync files=631, longest=0.002 s, average=0.001 s; distance=22828 kB, estimate=47797 kB; lsn=0/2F5DE8E8, redo lsn=0/2D62E6D8
2025-05-29 17:52:15.875 CST [29448] LOG:  checkpoint starting: time
2025-05-29 17:54:21.568 CST [33016] ERROR:  update or delete on table "res_company" violates foreign key constraint "account_journal_company_id_fkey" on table "account_journal"
2025-05-29 17:54:21.568 CST [33016] DETAIL:  Key (id)=(2) is still referenced from table "account_journal".
2025-05-29 17:54:21.568 CST [33016] STATEMENT:  DELETE FROM "res_company" WHERE id IN (2)
2025-05-29 17:56:45.160 CST [29448] LOG:  checkpoint complete: wrote 2744 buffers (16.7%); 0 WAL file(s) added, 0 removed, 2 recycled; write=269.137 s, sync=0.138 s, total=269.285 s; sync files=569, longest=0.003 s, average=0.001 s; distance=32448 kB, estimate=46262 kB; lsn=0/2F9B58C0, redo lsn=0/2F5DEA58
2025-05-29 18:00:45.574 CST [28812] ERROR:  could not serialize access due to concurrent update
2025-05-29 18:00:45.574 CST [28812] STATEMENT:   UPDATE "iap_account"
	                    SET "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (1, '2025-05-29 10:00:44.465095', 2)) AS "__tmp"("id", "write_date", "write_uid")
	                    WHERE "iap_account"."id" = "__tmp"."id"
	                
2025-05-29 18:02:15.176 CST [29448] LOG:  checkpoint starting: time
2025-05-29 18:03:03.823 CST [29448] LOG:  checkpoint complete: wrote 449 buffers (2.7%); 0 WAL file(s) added, 0 removed, 0 recycled; write=48.620 s, sync=0.022 s, total=48.647 s; sync files=86, longest=0.002 s, average=0.001 s; distance=3946 kB, estimate=42031 kB; lsn=0/2F9B9560, redo lsn=0/2F9B9528
2025-05-29 18:06:24.795 CST [14992] ERROR:  duplicate key value violates unique constraint "account_journal_code_company_uniq"
2025-05-29 18:06:24.795 CST [14992] DETAIL:  Key (company_id, code)=(1, 杂项) already exists.
2025-05-29 18:06:24.795 CST [14992] STATEMENT:  UPDATE "public"."account_journal" SET "company_id" = 1 WHERE "id" = 3
2025-05-29 18:07:07.620 CST [14992] ERROR:  update or delete on table "account_journal" violates foreign key constraint "account_move_journal_id_fkey" on table "account_move"
2025-05-29 18:07:07.620 CST [14992] DETAIL:  Key (id)=(3) is still referenced from table "account_move".
2025-05-29 18:07:07.620 CST [14992] STATEMENT:  DELETE FROM "public"."account_journal" WHERE "id" = 3
2025-05-29 18:07:11.774 CST [14992] ERROR:  update or delete on table "account_journal" violates foreign key constraint "account_move_journal_id_fkey" on table "account_move"
2025-05-29 18:07:11.774 CST [14992] DETAIL:  Key (id)=(3) is still referenced from table "account_move".
2025-05-29 18:07:11.774 CST [14992] STATEMENT:  DELETE FROM "public"."account_journal" WHERE "id" = 3
2025-05-29 18:07:15.826 CST [29448] LOG:  checkpoint starting: time
2025-05-29 18:07:17.332 CST [14992] ERROR:  update or delete on table "account_journal" violates foreign key constraint "account_move_journal_id_fkey" on table "account_move"
2025-05-29 18:07:17.332 CST [14992] DETAIL:  Key (id)=(3) is still referenced from table "account_move".
2025-05-29 18:07:17.332 CST [14992] STATEMENT:  DELETE FROM "public"."account_journal" WHERE "id" = 3
2025-05-29 18:07:19.220 CST [29448] LOG:  checkpoint complete: wrote 34 buffers (0.2%); 0 WAL file(s) added, 0 removed, 0 recycled; write=3.382 s, sync=0.007 s, total=3.394 s; sync files=23, longest=0.002 s, average=0.001 s; distance=72 kB, estimate=37835 kB; lsn=0/2F9CDA98, redo lsn=0/2F9CB5F8
2025-05-29 18:07:20.911 CST [14992] ERROR:  update or delete on table "account_journal" violates foreign key constraint "account_move_journal_id_fkey" on table "account_move"
2025-05-29 18:07:20.911 CST [14992] DETAIL:  Key (id)=(3) is still referenced from table "account_move".
2025-05-29 18:07:20.911 CST [14992] STATEMENT:  DELETE FROM "public"."account_journal" WHERE "id" = 3
2025-05-29 18:07:24.639 CST [14992] ERROR:  update or delete on table "account_journal" violates foreign key constraint "account_move_journal_id_fkey" on table "account_move"
2025-05-29 18:07:24.639 CST [14992] DETAIL:  Key (id)=(3) is still referenced from table "account_move".
2025-05-29 18:07:24.639 CST [14992] STATEMENT:  DELETE FROM "public"."account_journal" WHERE "id" = 3
2025-05-29 18:07:30.178 CST [14992] ERROR:  update or delete on table "account_journal" violates foreign key constraint "account_move_journal_id_fkey" on table "account_move"
2025-05-29 18:07:30.178 CST [14992] DETAIL:  Key (id)=(3) is still referenced from table "account_move".
2025-05-29 18:07:30.178 CST [14992] STATEMENT:  DELETE FROM "public"."account_journal" WHERE "id" = 3
2025-05-29 18:12:07.017 CST [33016] ERROR:  update or delete on table "account_journal" violates foreign key constraint "account_move_journal_id_fkey" on table "account_move"
2025-05-29 18:12:07.017 CST [33016] DETAIL:  Key (id)=(3) is still referenced from table "account_move".
2025-05-29 18:12:07.017 CST [33016] STATEMENT:  DELETE FROM "account_journal" WHERE id IN (1, 2, 6, 3, 4, 5, 7)
2025-05-29 18:12:15.232 CST [29448] LOG:  checkpoint starting: time
2025-05-29 18:12:18.739 CST [29448] LOG:  checkpoint complete: wrote 33 buffers (0.2%); 0 WAL file(s) added, 0 removed, 0 recycled; write=3.491 s, sync=0.009 s, total=3.507 s; sync files=27, longest=0.002 s, average=0.001 s; distance=81 kB, estimate=34059 kB; lsn=0/2F9DFC80, redo lsn=0/2F9DFC48
2025-05-29 18:12:36.616 CST [33016] ERROR:  update or delete on table "account_tax" violates foreign key constraint "account_reconcile_model_line_account_tax_re_account_tax_id_fkey" on table "account_reconcile_model_line_account_tax_rel"
2025-05-29 18:12:36.616 CST [33016] DETAIL:  Key (id)=(4) is still referenced from table "account_reconcile_model_line_account_tax_rel".
2025-05-29 18:12:36.616 CST [33016] STATEMENT:  DELETE FROM "account_tax" WHERE id IN (1, 2, 3, 4, 5, 6)
2025-05-29 18:14:19.406 CST [33016] ERROR:  update or delete on table "res_company" violates foreign key constraint "res_users_company_id_fkey" on table "res_users"
2025-05-29 18:14:19.406 CST [33016] DETAIL:  Key (id)=(1) is still referenced from table "res_users".
2025-05-29 18:14:19.406 CST [33016] STATEMENT:  DELETE FROM "res_company" WHERE id IN (1)
2025-05-29 18:17:13.703 CST [14992] FATAL:  terminating connection due to administrator command
2025-05-29 18:17:13.703 CST [39768] FATAL:  terminating connection due to administrator command
2025-05-29 18:17:13.704 CST [37536] FATAL:  terminating connection due to administrator command
2025-05-29 18:17:13.822 CST [29448] LOG:  checkpoint starting: immediate force wait
2025-05-29 18:17:13.832 CST [29448] LOG:  checkpoint complete: wrote 23 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.002 s, sync=0.005 s, total=0.010 s; sync files=13, longest=0.001 s, average=0.001 s; distance=1486 kB, estimate=30802 kB; lsn=0/2FB536D0, redo lsn=0/2FB53698
2025-05-29 18:19:44.613 CST [19780] ERROR:  null value in column "journal_id" of relation "account_move" violates not-null constraint
2025-05-29 18:19:44.613 CST [19780] DETAIL:  Failing row contains (1, null, null, null, 1, null, null, null, null, null, null, null, 9, null, 9, null, null, null, null, null, 6, null, null, 1, 1, null, null, null, null, draft, out_invoice, no, null, null, null, null, null, null, null, null, 2025-01-01, null, 2025-01-01, null, null, null, null, 1.0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 2025-05-29 10:19:43.346379, 2025-05-29 10:19:43.346379, null, null).
2025-05-29 18:19:44.613 CST [19780] STATEMENT:  INSERT INTO "account_move" ("auto_post", "company_id", "create_date", "create_uid", "currency_id", "date", "delivery_date", "fiscal_position_id", "invoice_currency_rate", "invoice_date", "invoice_incoterm_id", "invoice_payment_term_id", "invoice_user_id", "journal_id", "move_type", "partner_id", "partner_shipping_id", "state", "write_date", "write_uid") VALUES ('no', 1, '2025-05-29 10:19:43.346379', 1, NULL, '2025-01-01', NULL, NULL, 1.0, '2025-01-01', NULL, NULL, 6, NULL, 'out_invoice', 9, 9, 'draft', '2025-05-29 10:19:43.346379', 1) RETURNING "id"
2025-05-29 18:22:13.834 CST [29448] LOG:  checkpoint starting: time
2025-05-29 18:26:43.895 CST [29448] LOG:  checkpoint complete: wrote 6438 buffers (39.3%); 0 WAL file(s) added, 0 removed, 5 recycled; write=269.240 s, sync=0.807 s, total=270.062 s; sync files=2390, longest=0.006 s, average=0.001 s; distance=78295 kB, estimate=78295 kB; lsn=0/34908E90, redo lsn=0/347C92C8
2025-05-29 18:32:13.940 CST [29448] LOG:  checkpoint starting: time
2025-05-29 18:34:13.832 CST [29448] LOG:  checkpoint complete: wrote 1100 buffers (6.7%); 0 WAL file(s) added, 0 removed, 1 recycled; write=119.826 s, sync=0.058 s, total=119.892 s; sync files=221, longest=0.003 s, average=0.001 s; distance=11853 kB, estimate=71650 kB; lsn=0/3535C788, redo lsn=0/3535C750
2025-05-29 18:47:13.847 CST [29448] LOG:  checkpoint starting: time
2025-05-29 18:47:17.571 CST [29448] LOG:  checkpoint complete: wrote 35 buffers (0.2%); 0 WAL file(s) added, 0 removed, 0 recycled; write=3.713 s, sync=0.006 s, total=3.724 s; sync files=18, longest=0.002 s, average=0.001 s; distance=83 kB, estimate=64494 kB; lsn=0/353713B8, redo lsn=0/35371380
2025-05-29 18:52:13.584 CST [29448] LOG:  checkpoint starting: time
2025-05-29 18:54:12.554 CST [29448] LOG:  checkpoint complete: wrote 1093 buffers (6.7%); 0 WAL file(s) added, 0 removed, 0 recycled; write=118.878 s, sync=0.086 s, total=118.970 s; sync files=261, longest=0.003 s, average=0.001 s; distance=7532 kB, estimate=58797 kB; lsn=0/35ADB000, redo lsn=0/35ACC3A8
2025-05-29 22:34:52.877 CST [29288] ERROR:  could not serialize access due to concurrent update
2025-05-29 22:34:52.877 CST [29288] STATEMENT:  
	            WITH last_cron_progress AS (
	                SELECT id as progress_id, cron_id, timed_out_counter, done, remaining
	                FROM ir_cron_progress
	                WHERE cron_id = 3
	                ORDER BY id DESC
	                LIMIT 1
	            )
	            SELECT *
	            FROM ir_cron
	            LEFT JOIN last_cron_progress lcp ON lcp.cron_id = ir_cron.id
	            WHERE ir_cron.active = true
	              AND (nextcall <= (now() at time zone 'UTC')
	                OR EXISTS (
	                    SELECT cron_id
	                    FROM ir_cron_trigger
	                    WHERE call_at <= (now() at time zone 'UTC')
	                      AND cron_id = ir_cron.id
	                )
	              )
	              AND id = 3
	            ORDER BY priority
	            FOR NO KEY UPDATE SKIP LOCKED
	        
2025-05-29 22:37:24.188 CST [29448] LOG:  checkpoint starting: time
2025-05-29 22:37:33.006 CST [29448] LOG:  checkpoint complete: wrote 83 buffers (0.5%); 0 WAL file(s) added, 0 removed, 0 recycled; write=8.751 s, sync=0.030 s, total=8.819 s; sync files=56, longest=0.002 s, average=0.001 s; distance=274 kB, estimate=52945 kB; lsn=0/35B10D18, redo lsn=0/35B10CE0
2025-05-29 22:42:15.153 CST [8640] ERROR:  could not serialize access due to concurrent update
2025-05-29 22:42:15.153 CST [8640] STATEMENT:   UPDATE "res_users_settings"
	                    SET "is_discuss_sidebar_category_chat_open" = "__tmp"."is_discuss_sidebar_category_chat_open"::bool, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (1, false, '2025-05-29 14:42:15.087991', 2)) AS "__tmp"("id", "is_discuss_sidebar_category_chat_open", "write_date", "write_uid")
	                    WHERE "res_users_settings"."id" = "__tmp"."id"
	                
2025-05-29 22:42:24.012 CST [29448] LOG:  checkpoint starting: time
2025-05-29 22:43:25.613 CST [29448] LOG:  checkpoint complete: wrote 564 buffers (3.4%); 0 WAL file(s) added, 0 removed, 0 recycled; write=61.454 s, sync=0.111 s, total=61.602 s; sync files=149, longest=0.025 s, average=0.001 s; distance=3584 kB, estimate=48009 kB; lsn=0/35EA06E0, redo lsn=0/35E910B0
2025-05-29 22:43:27.892 CST [8640] ERROR:  could not serialize access due to concurrent update
2025-05-29 22:43:27.892 CST [8640] STATEMENT:   UPDATE "res_users_settings"
	                    SET "is_discuss_sidebar_category_chat_open" = "__tmp"."is_discuss_sidebar_category_chat_open"::bool, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (1, true, '2025-05-29 14:43:27.857351', 2)) AS "__tmp"("id", "is_discuss_sidebar_category_chat_open", "write_date", "write_uid")
	                    WHERE "res_users_settings"."id" = "__tmp"."id"
	                
2025-05-29 22:47:24.620 CST [29448] LOG:  checkpoint starting: time
2025-05-29 22:47:28.722 CST [29448] LOG:  checkpoint complete: wrote 40 buffers (0.2%); 0 WAL file(s) added, 0 removed, 0 recycled; write=4.064 s, sync=0.026 s, total=4.102 s; sync files=27, longest=0.003 s, average=0.001 s; distance=144 kB, estimate=43222 kB; lsn=0/35EB51C8, redo lsn=0/35EB5190
2025-05-29 22:52:24.732 CST [29448] LOG:  checkpoint starting: time
2025-05-29 22:52:29.084 CST [29448] LOG:  checkpoint complete: wrote 40 buffers (0.2%); 0 WAL file(s) added, 0 removed, 0 recycled; write=4.256 s, sync=0.058 s, total=4.352 s; sync files=36, longest=0.028 s, average=0.002 s; distance=102 kB, estimate=38910 kB; lsn=0/35ECEA20, redo lsn=0/35ECE9E8
2025-05-29 22:55:29.712 CST [8640] ERROR:  could not serialize access due to concurrent update
2025-05-29 22:55:29.712 CST [8640] STATEMENT:   UPDATE "res_users_settings"
	                    SET "is_discuss_sidebar_category_channel_open" = "__tmp"."is_discuss_sidebar_category_channel_open"::bool, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (1, false, '2025-05-29 14:55:29.671799', 2)) AS "__tmp"("id", "is_discuss_sidebar_category_channel_open", "write_date", "write_uid")
	                    WHERE "res_users_settings"."id" = "__tmp"."id"
	                
2025-05-29 22:55:30.251 CST [38988] ERROR:  could not serialize access due to concurrent update
2025-05-29 22:55:30.251 CST [38988] STATEMENT:   UPDATE "res_users_settings"
	                    SET "is_discuss_sidebar_category_channel_open" = "__tmp"."is_discuss_sidebar_category_channel_open"::bool, "write_date" = "__tmp"."write_date"::timestamp, "write_uid" = "__tmp"."write_uid"::int4
	                    FROM (VALUES (1, true, '2025-05-29 14:55:30.219394', 2)) AS "__tmp"("id", "is_discuss_sidebar_category_channel_open", "write_date", "write_uid")
	                    WHERE "res_users_settings"."id" = "__tmp"."id"
	                
2025-05-29 22:57:24.098 CST [29448] LOG:  checkpoint starting: time
2025-05-29 22:57:26.019 CST [29448] LOG:  checkpoint complete: wrote 18 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=1.860 s, sync=0.025 s, total=1.921 s; sync files=15, longest=0.006 s, average=0.002 s; distance=29 kB, estimate=35022 kB; lsn=0/35ED6080, redo lsn=0/35ED6048
2025-05-29 23:07:24.052 CST [29448] LOG:  checkpoint starting: time
2025-05-29 23:07:27.140 CST [29448] LOG:  checkpoint complete: wrote 29 buffers (0.2%); 0 WAL file(s) added, 0 removed, 0 recycled; write=3.057 s, sync=0.017 s, total=3.089 s; sync files=27, longest=0.002 s, average=0.001 s; distance=81 kB, estimate=31528 kB; lsn=0/35EEA570, redo lsn=0/35EEA538
2025-05-29 23:12:24.146 CST [29448] LOG:  checkpoint starting: time
2025-05-29 23:12:27.242 CST [29448] LOG:  checkpoint complete: wrote 29 buffers (0.2%); 0 WAL file(s) added, 0 removed, 0 recycled; write=3.071 s, sync=0.012 s, total=3.096 s; sync files=15, longest=0.002 s, average=0.001 s; distance=103 kB, estimate=28386 kB; lsn=0/35F04238, redo lsn=0/35F04200
2025-05-29 23:22:24.265 CST [29448] LOG:  checkpoint starting: time
2025-05-29 23:22:25.336 CST [29448] LOG:  checkpoint complete: wrote 12 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.989 s, sync=0.039 s, total=1.072 s; sync files=11, longest=0.026 s, average=0.004 s; distance=40 kB, estimate=25551 kB; lsn=0/35F0E408, redo lsn=0/35F0E3D0
2025-05-29 23:27:24.338 CST [29448] LOG:  checkpoint starting: time
2025-05-29 23:27:36.568 CST [10320] LOG:  skipping vacuum of "product_template" --- lock not available
2025-05-29 23:27:36.568 CST [10320] LOG:  skipping vacuum of "res_partner" --- lock not available
2025-05-29 23:27:36.568 CST [10320] LOG:  skipping analyze of "product_product" --- lock not available
2025-05-29 23:27:36.568 CST [10320] LOG:  skipping analyze of "account_journal" --- lock not available
2025-05-29 23:31:54.753 CST [29448] LOG:  checkpoint complete: wrote 2821 buffers (17.2%); 0 WAL file(s) added, 1 removed, 1 recycled; write=269.660 s, sync=0.707 s, total=270.415 s; sync files=1163, longest=0.026 s, average=0.001 s; distance=21649 kB, estimate=25161 kB; lsn=0/381FE318, redo lsn=0/37432A98
2025-05-29 23:37:24.802 CST [29448] LOG:  checkpoint starting: time
2025-05-29 23:39:57.055 CST [29448] LOG:  checkpoint complete: wrote 1389 buffers (8.5%); 0 WAL file(s) added, 0 removed, 1 recycled; write=152.018 s, sync=0.192 s, total=152.253 s; sync files=264, longest=0.006 s, average=0.001 s; distance=14126 kB, estimate=24057 kB; lsn=0/384EC030, redo lsn=0/381FE5A8
2025-05-29 23:42:24.069 CST [29448] LOG:  checkpoint starting: time
2025-05-30 08:18:27.167 CST [29448] LOG:  checkpoint complete: wrote 512 buffers (3.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=30962.564 s, sync=0.525 s, total=30963.098 s; sync files=33, longest=0.455 s, average=0.016 s; distance=3009 kB, estimate=21952 kB; lsn=0/384F0A10, redo lsn=0/384EE9B8
2025-05-30 08:23:27.181 CST [29448] LOG:  checkpoint starting: time
2025-05-30 08:24:34.176 CST [29448] LOG:  checkpoint complete: wrote 618 buffers (3.8%); 0 WAL file(s) added, 0 removed, 0 recycled; write=66.975 s, sync=0.016 s, total=66.996 s; sync files=55, longest=0.002 s, average=0.001 s; distance=3315 kB, estimate=20089 kB; lsn=0/38B0F7B8, redo lsn=0/3882B698
2025-05-30 08:28:27.192 CST [29448] LOG:  checkpoint starting: time
2025-05-30 08:29:23.804 CST [29448] LOG:  checkpoint complete: wrote 521 buffers (3.2%); 0 WAL file(s) added, 0 removed, 0 recycled; write=56.598 s, sync=0.010 s, total=56.613 s; sync files=32, longest=0.003 s, average=0.001 s; distance=4344 kB, estimate=18514 kB; lsn=0/38C69718, redo lsn=0/38C696E0
2025-05-30 08:58:27.860 CST [29448] LOG:  checkpoint starting: time
2025-05-30 08:58:28.645 CST [29448] LOG:  checkpoint complete: wrote 8 buffers (0.0%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.772 s, sync=0.006 s, total=0.785 s; sync files=8, longest=0.003 s, average=0.001 s; distance=16 kB, estimate=16664 kB; lsn=0/38C6D948, redo lsn=0/38C6D910
2025-05-30 09:23:27.696 CST [29448] LOG:  checkpoint starting: time
2025-05-30 09:23:31.512 CST [29448] LOG:  checkpoint complete: wrote 38 buffers (0.2%); 0 WAL file(s) added, 0 removed, 0 recycled; write=3.801 s, sync=0.010 s, total=3.817 s; sync files=23, longest=0.003 s, average=0.001 s; distance=155 kB, estimate=15013 kB; lsn=0/38C94728, redo lsn=0/38C946F0
2025-05-30 09:38:27.534 CST [29448] LOG:  checkpoint starting: time
2025-05-30 09:38:28.950 CST [29448] LOG:  checkpoint complete: wrote 14 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=1.405 s, sync=0.008 s, total=1.417 s; sync files=13, longest=0.003 s, average=0.001 s; distance=13 kB, estimate=13513 kB; lsn=0/38C97EB8, redo lsn=0/38C97E80
2025-05-30 09:43:27.961 CST [29448] LOG:  checkpoint starting: time
2025-05-30 09:43:30.038 CST [29448] LOG:  checkpoint complete: wrote 19 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=2.066 s, sync=0.005 s, total=2.078 s; sync files=7, longest=0.003 s, average=0.001 s; distance=27 kB, estimate=12165 kB; lsn=0/38C9EE98, redo lsn=0/38C9EE60
2025-05-30 10:06:29.657 CST [29448] LOG:  checkpoint starting: time
2025-05-30 10:06:30.534 CST [29448] LOG:  checkpoint complete: wrote 11 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.867 s, sync=0.006 s, total=0.878 s; sync files=11, longest=0.002 s, average=0.001 s; distance=23 kB, estimate=10951 kB; lsn=0/38CA4E08, redo lsn=0/38CA4DD0
2025-05-30 10:21:29.557 CST [29448] LOG:  checkpoint starting: time
2025-05-30 10:21:30.435 CST [29448] LOG:  checkpoint complete: wrote 11 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.867 s, sync=0.007 s, total=0.878 s; sync files=10, longest=0.003 s, average=0.001 s; distance=34 kB, estimate=9859 kB; lsn=0/38CAD6C0, redo lsn=0/38CAD688
2025-05-30 12:32:58.323 CST [29448] LOG:  checkpoint starting: time
2025-05-30 12:33:00.754 CST [29448] LOG:  checkpoint complete: wrote 24 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=2.343 s, sync=0.054 s, total=2.431 s; sync files=21, longest=0.025 s, average=0.003 s; distance=49 kB, estimate=8878 kB; lsn=0/38CB9BA8, redo lsn=0/38CB9B70
2025-05-30 12:33:59.676 CST [544] LOG:  received fast shutdown request
2025-05-30 12:33:59.677 CST [544] LOG:  aborting any active transactions
2025-05-30 12:33:59.678 CST [40228] FATAL:  terminating connection due to administrator command
2025-05-30 12:33:59.678 CST [3372] FATAL:  terminating connection due to administrator command
2025-05-30 12:33:59.678 CST [21392] FATAL:  terminating connection due to administrator command
2025-05-30 12:33:59.685 CST [544] LOG:  background worker "logical replication launcher" (PID 13640) exited with exit code 1
2025-05-30 12:33:59.687 CST [29448] LOG:  shutting down
2025-05-30 12:33:59.688 CST [29448] LOG:  checkpoint starting: shutdown immediate
2025-05-30 12:33:59.693 CST [29448] LOG:  checkpoint complete: wrote 1 buffers (0.0%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.001 s, sync=0.001 s, total=0.006 s; sync files=1, longest=0.001 s, average=0.001 s; distance=0 kB, estimate=7990 kB; lsn=0/38CB9C58, redo lsn=0/38CB9C58
2025-05-30 12:33:59.722 CST [544] LOG:  database system is shut down
2025-05-30 12:34:02.309 CST [45040] LOG:  starting PostgreSQL 16.4, compiled by Visual C++ build 1940, 64-bit
2025-05-30 12:34:02.313 CST [45040] LOG:  listening on IPv6 address "::1", port 5432
2025-05-30 12:34:02.313 CST [45040] LOG:  listening on IPv4 address "127.0.0.1", port 5432
2025-05-30 12:34:02.392 CST [43264] LOG:  database system was shut down at 2025-05-30 12:33:59 CST
2025-05-30 12:34:02.412 CST [45040] LOG:  database system is ready to accept connections
2025-05-30 12:34:10.605 CST [45040] LOG:  received fast shutdown request
2025-05-30 12:34:10.607 CST [45040] LOG:  aborting any active transactions
2025-05-30 12:34:10.608 CST [42944] FATAL:  terminating connection due to administrator command
2025-05-30 12:34:10.608 CST [42344] FATAL:  terminating connection due to administrator command
2025-05-30 12:34:10.609 CST [35024] FATAL:  terminating connection due to administrator command
2025-05-30 12:34:10.616 CST [45040] LOG:  background worker "logical replication launcher" (PID 44888) exited with exit code 1
2025-05-30 12:34:10.619 CST [43244] LOG:  shutting down
2025-05-30 12:34:10.620 CST [43244] LOG:  checkpoint starting: shutdown immediate
2025-05-30 12:34:10.628 CST [43244] LOG:  checkpoint complete: wrote 2 buffers (0.0%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.002 s, sync=0.002 s, total=0.010 s; sync files=3, longest=0.001 s, average=0.001 s; distance=0 kB, estimate=0 kB; lsn=0/38CB9CD0, redo lsn=0/38CB9CD0
2025-05-30 12:34:10.642 CST [45040] LOG:  database system is shut down
2025-05-30 14:45:37.329 CST [29296] LOG:  starting PostgreSQL 16.4, compiled by Visual C++ build 1940, 64-bit
2025-05-30 14:45:37.333 CST [29296] LOG:  listening on IPv6 address "::1", port 5432
2025-05-30 14:45:37.333 CST [29296] LOG:  listening on IPv4 address "127.0.0.1", port 5432
2025-05-30 14:45:37.406 CST [39740] LOG:  database system was shut down at 2025-05-30 12:34:10 CST
2025-05-30 14:45:37.427 CST [29296] LOG:  database system is ready to accept connections
2025-05-30 14:46:37.494 CST [29296] LOG:  could not reserve shared memory region (addr=000002016D540000) for child 000000000000154C: error code 487
2025-05-30 14:50:37.407 CST [2472] LOG:  checkpoint starting: time
2025-05-30 14:50:39.602 CST [2472] LOG:  checkpoint complete: wrote 24 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=2.182 s, sync=0.010 s, total=2.197 s; sync files=21, longest=0.003 s, average=0.001 s; distance=43 kB, estimate=43 kB; lsn=0/38CC4CA0, redo lsn=0/38CC4C68
2025-05-30 15:20:37.650 CST [2472] LOG:  checkpoint starting: time
2025-05-30 15:20:38.517 CST [2472] LOG:  checkpoint complete: wrote 12 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.858 s, sync=0.005 s, total=0.867 s; sync files=10, longest=0.002 s, average=0.001 s; distance=34 kB, estimate=42 kB; lsn=0/38CCD648, redo lsn=0/38CCD610
2025-05-30 15:50:37.576 CST [2472] LOG:  checkpoint starting: time
2025-05-30 15:50:38.999 CST [2472] LOG:  checkpoint complete: wrote 14 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=1.411 s, sync=0.007 s, total=1.424 s; sync files=13, longest=0.003 s, average=0.001 s; distance=11 kB, estimate=39 kB; lsn=0/38CD0608, redo lsn=0/38CD05D0
2025-05-30 15:55:38.006 CST [2472] LOG:  checkpoint starting: time
2025-05-30 15:56:34.202 CST [2472] LOG:  checkpoint complete: wrote 517 buffers (3.2%); 0 WAL file(s) added, 0 removed, 0 recycled; write=56.181 s, sync=0.010 s, total=56.196 s; sync files=39, longest=0.001 s, average=0.001 s; distance=2959 kB, estimate=2959 kB; lsn=0/38FB4248, redo lsn=0/38FB4210
2025-05-30 16:10:38.223 CST [2472] LOG:  checkpoint starting: time
2025-05-30 16:11:36.923 CST [2472] LOG:  checkpoint complete: wrote 539 buffers (3.3%); 0 WAL file(s) added, 0 removed, 1 recycled; write=58.685 s, sync=0.011 s, total=58.701 s; sync files=35, longest=0.002 s, average=0.001 s; distance=3016 kB, estimate=3016 kB; lsn=0/392A65E8, redo lsn=0/392A65B0
2025-05-30 16:21:03.594 CST [2472] LOG:  checkpoint starting: time
2025-05-30 16:21:05.012 CST [2472] LOG:  checkpoint complete: wrote 16 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=1.411 s, sync=0.004 s, total=1.419 s; sync files=11, longest=0.002 s, average=0.001 s; distance=65 kB, estimate=2721 kB; lsn=0/392B6AF0, redo lsn=0/392B6AB8
2025-05-30 16:26:03.019 CST [2472] LOG:  checkpoint starting: time
2025-05-30 16:26:03.786 CST [2472] LOG:  checkpoint complete: wrote 8 buffers (0.0%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.759 s, sync=0.004 s, total=0.768 s; sync files=4, longest=0.003 s, average=0.001 s; distance=45 kB, estimate=2454 kB; lsn=0/392C1F68, redo lsn=0/392C1F30
2025-05-30 16:41:46.190 CST [2472] LOG:  checkpoint starting: time
2025-05-30 16:41:46.751 CST [2472] LOG:  checkpoint complete: wrote 6 buffers (0.0%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.551 s, sync=0.004 s, total=0.561 s; sync files=6, longest=0.003 s, average=0.001 s; distance=6 kB, estimate=2209 kB; lsn=0/392C3AB8, redo lsn=0/392C3A80
2025-05-30 16:59:31.693 CST [2472] LOG:  checkpoint starting: time
2025-05-30 16:59:33.047 CST [2472] LOG:  checkpoint complete: wrote 13 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=1.337 s, sync=0.010 s, total=1.354 s; sync files=13, longest=0.003 s, average=0.001 s; distance=11 kB, estimate=1989 kB; lsn=0/392C6AB0, redo lsn=0/392C6A78
2025-05-30 17:04:31.048 CST [2472] LOG:  checkpoint starting: time
2025-05-30 17:04:31.594 CST [2472] LOG:  checkpoint complete: wrote 6 buffers (0.0%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.536 s, sync=0.005 s, total=0.546 s; sync files=6, longest=0.003 s, average=0.001 s; distance=7 kB, estimate=1791 kB; lsn=0/392C88C0, redo lsn=0/392C8888
2025-05-30 17:19:31.618 CST [2472] LOG:  checkpoint starting: time
2025-05-30 17:19:34.455 CST [2472] LOG:  checkpoint complete: wrote 29 buffers (0.2%); 0 WAL file(s) added, 0 removed, 0 recycled; write=2.827 s, sync=0.006 s, total=2.838 s; sync files=14, longest=0.003 s, average=0.001 s; distance=138 kB, estimate=1626 kB; lsn=0/392EB298, redo lsn=0/392EB228
2025-05-30 17:24:31.467 CST [2472] LOG:  checkpoint starting: time
2025-05-30 17:24:32.233 CST [2472] LOG:  checkpoint complete: wrote 8 buffers (0.0%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.759 s, sync=0.004 s, total=0.767 s; sync files=7, longest=0.003 s, average=0.001 s; distance=7 kB, estimate=1464 kB; lsn=0/392ED178, redo lsn=0/392ED140
2025-05-30 17:54:31.271 CST [2472] LOG:  checkpoint starting: time
2025-05-30 17:54:31.819 CST [2472] LOG:  checkpoint complete: wrote 6 buffers (0.0%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.540 s, sync=0.004 s, total=0.549 s; sync files=6, longest=0.001 s, average=0.001 s; distance=8 kB, estimate=1318 kB; lsn=0/392EF180, redo lsn=0/392EF148
2025-05-30 17:59:31.821 CST [2472] LOG:  checkpoint starting: time
2025-05-30 18:00:28.142 CST [2472] LOG:  checkpoint complete: wrote 518 buffers (3.2%); 0 WAL file(s) added, 0 removed, 0 recycled; write=56.304 s, sync=0.012 s, total=56.322 s; sync files=37, longest=0.002 s, average=0.001 s; distance=2931 kB, estimate=2931 kB; lsn=0/395CBDD0, redo lsn=0/395CBD98
2025-05-30 18:19:31.182 CST [2472] LOG:  checkpoint starting: time
2025-05-30 18:20:10.617 CST [2472] LOG:  checkpoint complete: wrote 360 buffers (2.2%); 0 WAL file(s) added, 0 removed, 0 recycled; write=39.418 s, sync=0.012 s, total=39.435 s; sync files=50, longest=0.003 s, average=0.001 s; distance=2293 kB, estimate=2867 kB; lsn=0/398DB840, redo lsn=0/398093C8
2025-05-30 18:24:31.620 CST [2472] LOG:  checkpoint starting: time
2025-05-30 18:24:51.847 CST [2472] LOG:  checkpoint complete: wrote 189 buffers (1.2%); 0 WAL file(s) added, 0 removed, 0 recycled; write=20.216 s, sync=0.006 s, total=20.228 s; sync files=14, longest=0.002 s, average=0.001 s; distance=846 kB, estimate=2665 kB; lsn=0/398DCC20, redo lsn=0/398DCBE8
2025-05-30 18:29:31.853 CST [2472] LOG:  checkpoint starting: time
2025-05-30 18:30:27.252 CST [2472] LOG:  checkpoint complete: wrote 513 buffers (3.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=55.359 s, sync=0.032 s, total=55.399 s; sync files=136, longest=0.001 s, average=0.001 s; distance=5113 kB, estimate=5113 kB; lsn=0/39E53748, redo lsn=0/39DDB108
2025-05-30 18:34:31.265 CST [2472] LOG:  checkpoint starting: time
2025-05-30 18:34:52.220 CST [2472] LOG:  checkpoint complete: wrote 194 buffers (1.2%); 0 WAL file(s) added, 0 removed, 0 recycled; write=20.937 s, sync=0.013 s, total=20.955 s; sync files=44, longest=0.002 s, average=0.001 s; distance=636 kB, estimate=4665 kB; lsn=0/39E95888, redo lsn=0/39E7A4A8
2025-05-30 18:39:31.234 CST [2472] LOG:  checkpoint starting: time
2025-05-30 18:40:37.714 CST [2472] LOG:  checkpoint complete: wrote 616 buffers (3.8%); 0 WAL file(s) added, 0 removed, 1 recycled; write=66.449 s, sync=0.024 s, total=66.480 s; sync files=77, longest=0.003 s, average=0.001 s; distance=3814 kB, estimate=4580 kB; lsn=0/3A2340A0, redo lsn=0/3A234068
2025-05-30 18:44:31.721 CST [2472] LOG:  checkpoint starting: time
2025-05-30 18:44:34.124 CST [2472] LOG:  checkpoint complete: wrote 23 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=2.393 s, sync=0.005 s, total=2.403 s; sync files=11, longest=0.003 s, average=0.001 s; distance=64 kB, estimate=4128 kB; lsn=0/3A244218, redo lsn=0/3A2441E0
2025-05-30 18:49:31.127 CST [2472] LOG:  checkpoint starting: time
2025-05-30 18:50:38.028 CST [2472] LOG:  checkpoint complete: wrote 601 buffers (3.7%); 0 WAL file(s) added, 0 removed, 0 recycled; write=66.874 s, sync=0.020 s, total=66.902 s; sync files=83, longest=0.002 s, average=0.001 s; distance=7839 kB, estimate=7839 kB; lsn=0/3AAFBF58, redo lsn=0/3A9EC188
2025-05-30 18:54:31.038 CST [2472] LOG:  checkpoint starting: time
2025-05-30 18:55:39.710 CST [2472] LOG:  checkpoint complete: wrote 634 buffers (3.9%); 0 WAL file(s) added, 0 removed, 0 recycled; write=68.646 s, sync=0.021 s, total=68.672 s; sync files=82, longest=0.003 s, average=0.001 s; distance=4886 kB, estimate=7544 kB; lsn=0/3AFBA060, redo lsn=0/3AEB1D30
2025-05-30 18:59:31.720 CST [2472] LOG:  checkpoint starting: time
2025-05-30 19:00:05.931 CST [2472] LOG:  checkpoint complete: wrote 319 buffers (1.9%); 0 WAL file(s) added, 0 removed, 0 recycled; write=34.070 s, sync=0.099 s, total=34.212 s; sync files=90, longest=0.025 s, average=0.002 s; distance=1283 kB, estimate=6918 kB; lsn=0/3B00E6E8, redo lsn=0/3AFF2B50
2025-05-30 19:14:17.490 CST [2472] LOG:  checkpoint starting: time
2025-05-30 19:14:22.729 CST [2472] LOG:  checkpoint complete: wrote 49 buffers (0.3%); 0 WAL file(s) added, 0 removed, 1 recycled; write=5.221 s, sync=0.013 s, total=5.240 s; sync files=42, longest=0.003 s, average=0.001 s; distance=163 kB, estimate=6243 kB; lsn=0/3B01BA98, redo lsn=0/3B01BA60
2025-05-30 19:19:17.737 CST [2472] LOG:  checkpoint starting: time
2025-05-30 19:19:24.272 CST [2472] LOG:  checkpoint complete: wrote 63 buffers (0.4%); 0 WAL file(s) added, 0 removed, 0 recycled; write=6.511 s, sync=0.017 s, total=6.535 s; sync files=51, longest=0.002 s, average=0.001 s; distance=201 kB, estimate=5638 kB; lsn=0/3B04DFD8, redo lsn=0/3B04DFA0
2025-05-30 19:24:17.277 CST [2472] LOG:  checkpoint starting: time
2025-05-30 19:24:19.564 CST [2472] LOG:  checkpoint complete: wrote 24 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=2.274 s, sync=0.009 s, total=2.287 s; sync files=23, longest=0.003 s, average=0.001 s; distance=70 kB, estimate=5082 kB; lsn=0/3B05F9F0, redo lsn=0/3B05F9B8
2025-05-30 20:07:31.070 CST [2472] LOG:  checkpoint starting: time
2025-05-30 20:07:32.490 CST [2472] LOG:  checkpoint complete: wrote 14 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=1.406 s, sync=0.008 s, total=1.421 s; sync files=13, longest=0.002 s, average=0.001 s; distance=21 kB, estimate=4575 kB; lsn=0/3B064F50, redo lsn=0/3B064F18
2025-05-30 20:12:31.494 CST [2472] LOG:  checkpoint starting: time
2025-05-30 20:13:37.868 CST [2472] LOG:  checkpoint complete: wrote 616 buffers (3.8%); 0 WAL file(s) added, 0 removed, 0 recycled; write=66.347 s, sync=0.023 s, total=66.374 s; sync files=92, longest=0.002 s, average=0.001 s; distance=3799 kB, estimate=4498 kB; lsn=0/3B422288, redo lsn=0/3B41AC70
2025-05-31 13:49:11.284 CST [2472] LOG:  checkpoint starting: time
2025-05-31 13:49:22.165 CST [2472] LOG:  checkpoint complete: wrote 102 buffers (0.6%); 0 WAL file(s) added, 0 removed, 0 recycled; write=10.798 s, sync=0.047 s, total=10.881 s; sync files=75, longest=0.002 s, average=0.001 s; distance=345 kB, estimate=4083 kB; lsn=0/3B471488, redo lsn=0/3B471450
2025-05-31 14:19:11.219 CST [2472] LOG:  checkpoint starting: time
2025-05-31 14:19:12.223 CST [2472] LOG:  checkpoint complete: wrote 12 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.952 s, sync=0.016 s, total=1.004 s; sync files=11, longest=0.006 s, average=0.002 s; distance=35 kB, estimate=3678 kB; lsn=0/3B47A110, redo lsn=0/3B47A0D8
2025-05-31 14:24:11.240 CST [2472] LOG:  checkpoint starting: time
2025-05-31 14:24:13.271 CST [2472] LOG:  checkpoint complete: wrote 19 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=1.959 s, sync=0.033 s, total=2.031 s; sync files=4, longest=0.030 s, average=0.009 s; distance=121 kB, estimate=3322 kB; lsn=0/3B498620, redo lsn=0/3B4985E8
2025-05-31 14:29:11.277 CST [2472] LOG:  checkpoint starting: time
2025-05-31 14:29:12.632 CST [2472] LOG:  checkpoint complete: wrote 13 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=1.301 s, sync=0.039 s, total=1.355 s; sync files=9, longest=0.028 s, average=0.005 s; distance=19 kB, estimate=2992 kB; lsn=0/3B49D360, redo lsn=0/3B49D328
2025-05-31 14:34:11.636 CST [2472] LOG:  checkpoint starting: time
2025-05-31 14:34:17.147 CST [2472] LOG:  checkpoint complete: wrote 51 buffers (0.3%); 0 WAL file(s) added, 0 removed, 0 recycled; write=5.409 s, sync=0.066 s, total=5.512 s; sync files=42, longest=0.026 s, average=0.002 s; distance=164 kB, estimate=2709 kB; lsn=0/3B4C6720, redo lsn=0/3B4C66E8
2025-05-31 14:39:11.154 CST [2472] LOG:  checkpoint starting: time
2025-05-31 14:39:12.094 CST [2472] LOG:  checkpoint complete: wrote 9 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.870 s, sync=0.032 s, total=0.940 s; sync files=4, longest=0.027 s, average=0.008 s; distance=39 kB, estimate=2442 kB; lsn=0/3B4D0528, redo lsn=0/3B4D04F0
2025-05-31 14:41:30.510 CST [29296] LOG:  could not reserve shared memory region (addr=000002016D540000) for child 0000000000000E60: error code 487
2025-05-31 15:14:11.149 CST [2472] LOG:  checkpoint starting: time
2025-05-31 15:14:12.856 CST [2472] LOG:  checkpoint complete: wrote 16 buffers (0.1%); 0 WAL file(s) added, 0 removed, 0 recycled; write=1.664 s, sync=0.035 s, total=1.708 s; sync files=16, longest=0.025 s, average=0.003 s; distance=30 kB, estimate=2201 kB; lsn=0/3B4D7D28, redo lsn=0/3B4D7CF0
2025-06-01 00:10:06.822 CST [29296] LOG:  received fast shutdown request
2025-06-01 00:10:06.828 CST [29296] LOG:  aborting any active transactions
2025-06-01 00:10:06.858 CST [29296] LOG:  background worker "logical replication launcher" (PID 30688) exited with exit code 1
2025-06-01 00:10:06.860 CST [2472] LOG:  shutting down
2025-06-01 00:10:06.862 CST [2472] LOG:  checkpoint starting: shutdown immediate
2025-06-01 00:10:06.944 CST [2472] LOG:  checkpoint complete: wrote 99 buffers (0.6%); 0 WAL file(s) added, 0 removed, 0 recycled; write=0.021 s, sync=0.051 s, total=0.084 s; sync files=63, longest=0.004 s, average=0.001 s; distance=401 kB, estimate=2021 kB; lsn=0/3B53C278, redo lsn=0/3B53C278
2025-06-01 00:10:06.994 CST [29296] LOG:  database system is shut down
