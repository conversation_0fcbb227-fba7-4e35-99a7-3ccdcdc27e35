# -*- coding: utf-8 -*-

import base64
import os
import requests, json
import logging
from openai import OpenAI
from http import HTTPStatus
from odoo import api, fields, models, modules, tools, _
from odoo.exceptions import UserError


_logger = logging.getLogger(__name__)


class AiRobot(models.Model):
    _inherit = 'ai.robot'

    provider = fields.Selection(selection_add=[('qwen', '通义千问qwen')], ondelete={'qwen': 'set default'})
    set_ai_model = fields.Selection(
        selection_add=[
            ('qwen-max', 'qwen-max-2024-09-19'),
            ('qwen-max-2025-01-25', 'qwen-max-2025-01-25'),
            ('qwen-plus', 'qwen-plus-2025-01-25'),
            ('qwen-turbo', 'qwen-turbo-2025-02-11'),
            ('deepseek-r1', 'deepseek-r1 671B 满血版模型'),
        ],
        ondelete={'qwen-max': 'set default'})

    @api.onchange('provider')
    def _onchange_provider(self):
        if self.provider == 'qwen':
            self.endpoint = 'https://dashscope.aliyuncs.com/compatible-mode/v1'
            # 用SDK调用时需配置的base_url：https://dashscope.aliyuncs.com/compatible-mode/v1
            # 使用HTTP方式调用时需配置的endpoint：POSTendpoint：POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions
            # 取头像
            module_path = modules.get_module_path('app_ali_qwen', display_warning=False)
            if module_path:
                path = modules.check_resource_path(module_path, ('static/description/src/%s.png' % self.provider))
                if path:
                    image_file = tools.file_open(path, 'rb')
                    self.image_avatar = base64.b64encode(image_file.read())
        return super()._onchange_provider()

    def get_qwen(self, data, author_id, answer_id, param={}):
        self.ensure_one()
        api_key = self.openapi_api_key
        if not api_key or not self.endpoint:
            raise UserError(_("Please provide Ai Robot [%s] API Key and Endpoint URL first." % self.name))

        if isinstance(data, list):
            messages = data
        else:
            messages = [{"role": "user", "content": data}]

        client = OpenAI(api_key=api_key, base_url=self.endpoint)
        response = client.chat.completions.create(
            model=self.ai_model,
            messages=messages,

        )
        res = response.model_dump()
        if 'choices' in res:
            return res
        else:
            _logger.warning('=====================qwen output data: %s' % response.json())

        return _("Response Timeout, please speak again.")

    def get_ai_post(self, res, author_id=False, answer_id=False, param={}):
        if self.provider == 'qwen':
            usage = res['usage']
            content = res['choices'][0]['message']['content']
            return content, usage, True
        else:
            return super().get_ai_post(res, author_id, answer_id, param)

    def get_ai_list_model(self):
        self.ensure_one()
        if self.provider == 'qwen':
            r_text = 'No response.'
            raise UserError(r_text)
        else:
            return super().get_ai_list_model()
