# -*- coding: utf-8 -*-

# Created on 2025-05-30
# author: 五哥，https://www.yunkuaiji.me
# email: <EMAIL>
# resource of yunkuaiji.me
# License AGPL-3.0 or later (http://www.gnu.org/licenses/agpl).

{
    'name': 'qwen Ai for odoo ai center, 通义千问Ai支持-对话模型',
    'version': '**********.30',
    'author': 'yunkuaiji.me',
    'company': 'yunkuaiji.me',
    'maintainer': 'yunkuaiji.me',
    'category': 'Website/Website',
    'website': 'https://www.yunkuaiji.me',
    'live_test_url': 'https://yunkuaiji.me',
    'license': 'LGPL-3',
    'sequence': 10,
    'license': 'LGPL-3',
    'price': 0.00,
    'currency': 'EUR',
    'images': ['static/description/qwen-color.png'],
    'summary': '''
    通义千问Ai支持-对话模型
    ''',
    'description': '''
   通义千问Ai支持-对话模型
    ''',
    'depends': [
        'app_chatgpt',
    ],
    'data': [
        'data/ai_robot_data.xml',
        'data/user_partner_data.xml',
        'data/discuss_channel_data.xml',
        'views/ai_robot_views.xml',
    ],
    'assets': {
    },
    'installable': True,
    'application': True,
    'auto_install': False,
}
