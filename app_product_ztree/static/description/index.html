<section class="oe_container container">
    <div class="oe_row oe_spaced" style="max-width: 95%;">
        <h2 class="oe_slogan" style="color:#875A7B;">App ztree widget, for parent children tree list. Demo for product category</h2>
        <h3 class="oe_slogan">Very useful for parent child relationship, like product category, stock location, hr department</h3>
    </div>
</section>

<section class="oe_container container">
    <div class="oe_row oe_spaced">
        <div class="row">
            <div class="alert alert-info" style="padding:8px;font-weight: 300; font-size: 20px;">
                <i class="fa fa-hand-o-right"></i><b> Key features: </b>
                <ul class="list-unstyled">
                    <li>
                        <i class="fa fa-check-square-o text-primary"></i>
                        Easy to make custom parent children tree.
                    </li>
                    <li>
                        <i class="fa fa-check-square-o text-primary"></i>
                        Free to Use in product category.
                    </li>
                    <li>
                        <i class="fa fa-check-square-o text-primary"></i>
                        Free to Use in stock location.
                    </li>
                    <li>
                        <i class="fa fa-check-square-o text-primary"></i>
                        Free to Use in hr department, employee.
                    </li>
                    <li>
                        <i class="fa fa-check-square-o text-primary"></i>
                        It's a widget, you can use it in anywhere in odoo.
                    </li>
                </ul>
            </div>
            <div class="alert alert-info" style="padding:8px;font-weight: 300; font-size: 20px;">
                <i class="fa fa-hand-o-right"></i>
                <b>
                    <a href="http://www.odoo.com/apps/modules/18.0/app_web_superbar/" target="_blank" class="text-danger">
                    Extend features. superbar advance search navigator
                    </a>
                </b>
                <ul class="list-unstyled">
                    <li>
                        <i class="fa fa-check-square-o text-primary"></i>
                        Easy to navigate product, employee in tree or kanban view.
                    </li>
                </ul>
            </div>
            <div class="alert alert-info" style="padding:8px;font-weight: 300; font-size: 20px;">
                <i class="fa fa-hand-o-right"></i><b>Get relate Demo </b>
                <ul class="list-unstyled">
                    <li>
                        <i class="fa fa-link text-primary"></i>
                        <a href="http://www.odoo.com/apps/modules/18.0/app_web_widget_ztree/" target="_blank" class="text-danger">zTree widget (must need to use in following demo).</a>
                    </li>
                    <li>
                        <i class="fa fa-link text-primary"></i>
                        <a href="http://www.odoo.com/apps/modules/18.0/app_product_ztree/" target="_blank">Product and category.</a>
                    </li>
                    <li>
                        <i class="fa fa-link text-primary"></i>
                        <a href="http://www.odoo.com/apps/modules/18.0/app_stock_ztree/" target="_blank">Stock and Location.</a>
                    </li>
                    <li>
                        <i class="fa fa-link text-primary"></i>
                        <a href="http://www.odoo.com/apps/modules/18.0/app_hr_ztree/" target="_blank">hr department and employee.</a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</section>

<section class="oe_container container">
    <div class="oe_row oe_spaced">
        <h4 class="oe_slogan">Use in product.</h4>
        <div class="oe_demo oe_screenshot img img-fluid">
            <img src="demo2.jpg">
        </div>
    </div>
</section>

<section class="oe_container container">
    <div class="oe_row oe_spaced">
        <h4 class="oe_slogan">Free to Use in product category.</h4>
        <div class="oe_demo oe_screenshot img img-fluid">
            <img src="demo1.jpg">
        </div>
    </div>
</section>

<section class="oe_container container">
    <div class="oe_row oe_spaced">
        <h4 class="oe_slogan">Free to Use in stock location.</h4>
        <div class="oe_demo oe_screenshot img img-fluid">
            <img src="demo3.jpg">
        </div>
    </div>
</section>

<section class="oe_container container">
    <div class="oe_row oe_spaced">
        <h4 class="oe_slogan">Use in hr department.</h4>
        <div class="oe_demo oe_screenshot img img-fluid">
            <img src="demo4.jpg">
        </div>
    </div>
</section>

<section class="oe_container container">
    <div class="oe_row oe_spaced">
        <h4 class="oe_slogan">Use in hr employee.</h4>
        <div class="oe_demo oe_screenshot img img-fluid">
            <img src="demo5.jpg">
        </div>
    </div>
</section>


<section class="oe_container container">
    <div class="oe_row oe_spaced">
        <h2>&nbsp;</h2>
        <h2 class="bg-warning text-center pt8 pb8 mt16 mb16">(!Need extra module and pay, release soon)Easy to navigator.</h2>
        <h4 class="oe_slogan">Product, employee in kanban view</h4>
        <div class="oe_demo oe_screenshot img img-fluid">
            <img src="demo11.jpg">
        </div>
    </div>
</section>

<section class="oe_container container">
    <div class="oe_row oe_spaced">
        <h4 class="oe_slogan">(!Need extra module and pay, release soon)List view</h4>
        <div class="oe_demo oe_screenshot img img-fluid">
            <img src="demo12.jpg">
        </div>
    </div>
</section>

<section class="oe_container container">
    <div class="oe_row oe_spaced">
        <h4 class="oe_slogan">Easy to setup just use widget='widget_select'. like this</h4>
        <div class="oe_demo oe_screenshot img img-fluid">
            <img src="setup1.jpg">
        </div>
        <h2><br/> Use follow param to setup widget:<br/></h2>
        <h4>ztree_parent_key: --the key field of parent children relation.</h4>
        <h4>ztree_expend_level: --how many level to expend the tree for initialize. Default is 2</h4>
        <h4>limit: --how many record to show ztree. Default is 16</h4>
        <h4>order: --the field to order by</h4>
    </div>
</section>
 <section class="container oe_dark">
    <div class="oe_row oe_spaced text-center">
        <div class="row">
            <h2 class="oe_slogan">Technical Help & Support</h2>
        </div>
        <div class="col-md-12 pad0">
            <div class="oe_mt16">
                <p><h4>
                For any type of technical help & support requests, Feel free to contact us</h4></p>
                <a style="background: #002e5a  none repeat scroll 0% 0%; color: rgb(255, 255, 255);position: relative; overflow: hidden;"
                   class="btn btn-warning btn-lg" rel="nofollow" href="mailto:<EMAIL>"><span
                        style="height: 354px; width: 354px; top: -147.433px; left: -6.93335px;" class="o_ripple"></span>
                    <i class="fa fa-envelope"></i> <EMAIL></a>
                <p><h4>
                Via QQ: 300883 (App user would not get QQ or any other IM support. Only for odoo project customize.)</h4></p>
                <a style="background: #002e5a  none repeat scroll 0% 0%; color: rgb(255, 255, 255);position: relative; overflow: hidden;"
                   class="btn btn-warning btn-lg" rel="nofollow" href="mailto:<EMAIL>"><span
                        style="height: 354px; width: 354px; top: -147.433px; left: -6.93335px;" class="o_ripple"></span>
                    <i class="fa fa-envelope"></i> <EMAIL></a>
            </div>
            <div class="oe_mt16">
                <h4>
                Visit our website for more support.</h4>
<h4>https://www.odooai.cn</h4>
            </div>
        </div>
    </div>
</section>

