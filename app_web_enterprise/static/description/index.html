<?xml version="1.0" encoding="utf-8"?>
<!--
# -*- coding: utf-8 -*-
##############################################################################
# Copyright (C) 2009~2024 odooAi.cn
##############################################################################
-->
<html>
<!-- begin title-->
<section class="oe_container container o_cc o_cc2">
    <h2 class="text-center bg-warning text-white pt24 pb24">odoo Enterprise version enhance Pack All in One</h2>
    <h3 class="text-center">Replace odoo logo with yours And Better UI for user</h3>
    <h4 class="text-center">将Odoo企业版OEM，同时使用更舒服的主色及界面，细节全面完善</h4>
</section>
<!-- end title-->

<!-- begin snapshot-->
<!-- quick demo-->

<section class="oe_container container">
    <div class="oe_row oe_spaced" style="max-width: 95%;">
        <div class="row">
            <div class="oe_demo" style=" margin: 30px auto 0; padding: 0 15px 0 0; border:none; width: 96%;">
                <h2 class="mt32 mb32">More Powerful addons:
                    <a class="btn btn-primary mb16" href="http://www.odoo.com/apps/modules/browse?author=odooai.cn">odooai.cn Odoo Addons</a>
                </h2>
                <h3>Latest update: v18.24.12.14</h3>
                <p>This moduld allows user to</p>
                <ul>
                    <li class="mb8">
                        1. Ui Enhance pack of odoo Enterprise version. Use comfortable green color
                        odoo企业版界面增强套件，更方便操作。使用更舒适护眼的绿色作为主色。
                    </li>
                    <li class="mb8">
                        2. Add dropdown arrow to parent menu group.
                        多级菜单中出现箭头，导航操作更方便。
                    </li>
                    <li class="mb8">
                        3. Replace the odoo logo or url to your company in menu and page.
                        替换主菜单界面的logo为你公司的logo。
                    </li>
                    <li class="mb8">
                        4. Add underline for input field.
                        在可编辑字段下方增加下划线，易于分辨。
                    </li>
                    <li class="mb8">
                        5. Add grid line form list view. Easy to read list data.
                        为表格list增加行列分隔线，易于看数据。
                    </li>
                    <li class="mb8">
                        6. Add grid line to Account Reports. Easy to view Data.
                        为财务报表增加行列分隔线，易于看数据及对账。
                    </li>
                </ul>
            </div>
        </div>
    </div>
</section>

<section class="oe_container container">
    <div class="oe_row oe_spaced">
        <h2 class="bg-warning text-center pt8 pb8">1. Ui Enhance pack of odoo Enterprise version. Use comfortable green color</h2>
        <div class="oe_demo  oe_screenshot img img-fluid">
            <img  src="app_web_enterprise_01.jpg"/>
        </div>
        <h4 class="oe_slogan"> Better for mobile</h4>
        <div class="oe_demo  oe_screenshot img img-fluid">
            <img  src="app_web_enterprise_02.jpg"/>
        </div>
        <h4 class="oe_slogan"> Better for DarkMode. Good for your Eyes</h4>
        <div class="oe_demo  oe_screenshot img img-fluid">
            <img  src="app_web_enterprise_03.jpg"/>
        </div>
        <h4 class="oe_slogan"> Easy config all UI and OEM. Navbar at bottom quick operation</h4>
        <div class="oe_demo  oe_screenshot img img-fluid">
            <img  src="app_web_enterprise_04.jpg"/>
        </div>
        <h4 class="oe_slogan"> The dropdown arrow to show you action menu or group menu.</h4>
        <div class="oe_demo  oe_screenshot img img-fluid">
            <img  src="app_web_enterprise_05.jpg"/>
        </div>
        <h4 class="oe_slogan"> Easy setup you logo and menu OEM. And quick remove data </h4>
        <div class="oe_demo  oe_screenshot img img-fluid">
            <img  src="app_web_enterprise_06.jpg"/>
        </div>
    </div>
</section>

<section class="oe_container container">
    <div class="oe_row oe_spaced">
        <h2 class="bg-warning text-center pt8 pb8">4. Add underline for input field.    在可编辑字段下方增加下划线，易于分辨。</h2>
        <h4 class="oe_slogan"> </h4>
        <div class="oe_demo  oe_screenshot img img-fluid">
            <img  src="b03.jpg"/>
        </div>
    </div>
</section>
<section class="oe_container container">
    <div class="oe_row oe_spaced">
        <h2 class="bg-warning text-center pt8 pb8">5. Add grid line form list view. Easy to read list data.为表格list增加行列分隔线，易于看数据。</h2>
        <h4 class="oe_slogan"> </h4>
        <div class="oe_demo  oe_screenshot img img-fluid">
            <img  src="b04.jpg"/>
        </div>
    </div>
</section>

<section class="oe_container container">
    <div class="oe_row oe_spaced">
        <h2 class="bg-warning text-center pt8 pb8">6. Add grid line to Account Reports. Easy to view Data.为财务报表增加行列分隔线，易于看数据及对账。</h2>
        <h4 class="oe_slogan"> </h4>
        <div class="oe_demo  oe_screenshot img img-fluid">
            <img  src="b05.jpg"/>
        </div>
    </div>
</section>
<section class="oe_container container">
    <div class="oe_row oe_spaced">
        <h2 class="bg-warning text-center pt8 pb8">Multi language support. 多语种支持</h2>
        <h4 class="oe_slogan"> </h4>
        <div class="oe_demo  oe_screenshot img img-fluid">
            <img  src="set21.jpg"/>
        </div>
    </div>
</section>

<!-- end quick demo-->

 <section class="container oe_dark">
    <div class="oe_row oe_spaced text-center">
        <div class="row">
            <h2 class="oe_slogan">Technical Help & Support</h2>
        </div>
        <div class="col-md-12 pad0">
            <div class="oe_mt16">
                <p><h4>
                For any type of technical help & support requests, Feel free to contact us</h4></p>
                <a style="background: #002e5a  none repeat scroll 0% 0%; color: rgb(255, 255, 255);position: relative; overflow: hidden;"
                   class="btn btn-warning btn-lg" rel="nofollow" href="mailto:<EMAIL>"><span
                        style="height: 354px; width: 354px; top: -147.433px; left: -6.93335px;" class="o_ripple"></span>
                    <i class="fa fa-envelope"></i> <EMAIL></a>
                <p><h4>
                Via QQ: 300883 (App user would not get QQ or any other IM support. Only for odoo project customize.)</h4></p>
                <a style="background: #002e5a  none repeat scroll 0% 0%; color: rgb(255, 255, 255);position: relative; overflow: hidden;"
                   class="btn btn-warning btn-lg" rel="nofollow" href="mailto:<EMAIL>"><span
                        style="height: 354px; width: 354px; top: -147.433px; left: -6.93335px;" class="o_ripple"></span>
                    <i class="fa fa-envelope"></i> <EMAIL></a>
            </div>
            <div class="oe_mt16">
                <h4>
                Visit our website for more support.</h4>
<h4>https://www.odooai.cn</h4>
            </div>
        </div>
    </div>
</section>

