<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">
<!--    tip用 *-->
    <t t-name="app_web_enterprise.FormLabel" t-inherit="web.FormLabel" t-inherit-mode="extension">
        <xpath expr="//label" position="replace">
            <label class="o_form_label" t-att-for="props.id" t-att-class="className" >
                <t t-esc="props.string"/>
                <sup class="btn-link p-1" t-if="hasTooltip" t-att="{'data-tooltip-template': 'web.FieldTooltip', 'data-tooltip-info': tooltipInfo, 'data-tooltip-touch-tap-to-show': 'true'}">*</sup>
            </label>
        </xpath>
    </t>
    <!--   更紧凑，先不处理 保存增加字样-->
<!--    <t t-name="app_web_enterprise.FormStatusIndicator" t-inherit="web.FormStatusIndicator" t-inherit-mode="extension">-->
<!--        <xpath expr="//i[hasclass('fa-cloud-upload')]" position="after">-->
<!--            Save-->
<!--        </xpath>-->
<!--        <xpath expr="//i[hasclass('fa-undo')]" position="after">-->
<!--            Discard-->
<!--        </xpath>-->
<!--    </t>-->
    <!--    17不需要，默认就是创建移到左边-->
<!--    <t t-name="app_web_enterprise.FormControlPanel" t-inherit="web.FormControlPanel" t-inherit-mode="extension">-->
<!--        <xpath expr="//div[hasclass('o_cp_bottom_right')]//t[3]" position="replace"/>-->
<!--&lt;!&ndash;        <xpath expr="//t[t-slot='control-panel-create-button']" position="replace"/>&ndash;&gt;-->
<!--&lt;!&ndash;        <xpath expr="//div[hasclass('o_cp_top_left')]" position="replace"/>&ndash;&gt;-->
<!--        <xpath expr="//div[hasclass('o_cp_top_left')]" position="inside">-->
<!--            <t t-slot="control-panel-create-button" />-->
<!--        </xpath>-->
<!--    </t>-->
</templates>
