<?xml version="1.0" encoding="utf-8"?>
<templates xml:space="preserve">
<!--    <t t-name="mail.TrackingValue" t-inherit="mail.TrackingValue" t-inherit_mode="extension" owl="1">-->
<!--        <xpath expr="//span[hasclass('o_TrackingValue_fieldName')]" position="replace">-->
<!--            <span class="o_TrackingValue_fieldName ms-1 fw-bold text-muted" t-esc="trackingValue.formattedChangedField + ':'"/>-->
<!--        </xpath>-->
<!--        <xpath expr="//span[@t-esc='trackingValue.oldValue.formattedValueOrNone']" position="before">-->
<!--            <xpath expr="//span[hasclass('o_TrackingValue_fieldName')]" position="move"/>-->
<!--        </xpath>-->
<!--    </t>-->
</templates>
