<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
<!--        <function name="install_lang" model="res.lang"/>-->

<!--        <record id="base.main_partner" model="res.partner" context="{'default_is_company': True}">-->
<!--            <field name="name">欧度智能</field>-->
<!--        </record>-->

<!--        &lt;!&ndash; Basic Company  &ndash;&gt;-->
<!--        <record id="base.main_company" model="res.company">-->
<!--            <field name="name">欧度智能</field>-->
<!--        </record>-->

<!--        <record model="res.partner" id="base.partner_root">-->
<!--            <field name="name">超管</field>-->
<!--        </record>-->
    </data>
</odoo>
